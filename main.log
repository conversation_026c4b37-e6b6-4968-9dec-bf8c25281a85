2025-08-26 21:24:50,424 - Main - INFO - 启动认知智能体模拟系统...
2025-08-26 21:24:50,424 - Engine - INFO - Initializing Engine and its modules...
2025-08-26 21:24:50,424 - src.modules.agent_module - INFO - AgentModule initialized.
2025-08-26 21:24:50,424 - src.modules.content_module - INFO - ContentModule initialized.
2025-08-26 21:24:50,424 - src.modules.agent_module - INFO - AgentModule initialized.
2025-08-26 21:24:50,425 - NotificationModule - INFO - NotificationModule initialized.
2025-08-26 21:24:50,425 - src.modules.belief_module - INFO - BeliefModule initialized with dependencies.
2025-08-26 21:24:50,425 - src.modules.belief_module - INFO - 多线程配置: {'enabled': True, 'max_workers': 4, 'timeout': 30}
2025-08-26 21:24:50,425 - UserManagement - INFO - UserManagementModule initialized.
2025-08-26 21:24:50,425 - Engine - INFO - Engine and modules initialized successfully.
2025-08-26 21:24:50,425 - Simulator - INFO - Simulator initialized with initial_bias_strength=1.
2025-08-26 21:24:50,425 - NotificationModule - INFO - Simulator set for NotificationModule.
2025-08-26 21:24:50,425 - Engine - INFO - Simulator setup completed and linked with notification module.
2025-08-26 21:24:50,425 - BeliefLogger - INFO - 实验结果将保存到: /home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250826_212450
2025-08-26 21:24:50,426 - BeliefLogger - INFO - 实验配置已保存到: experiment_config.json
2025-08-26 21:24:50,427 - Main - INFO - 开始运行 40 轮模拟，每轮 5 步...
2025-08-26 21:24:50,427 - Main - INFO - 开始第 1/40 轮模拟
2025-08-26 21:24:57,171 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户信念到 round_1_before_beliefs.json
2025-08-26 21:24:57,470 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户记忆到 round_1_before_memories.json
2025-08-26 21:24:57,761 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户信息到 round_1_before_users.json，共 100 个用户
2025-08-26 21:25:02,969 - ExperimentLoggers - INFO - 已记录轮次 1 before 阶段的所有帖子、评论和回复到 round_1_before_posts.json
2025-08-26 21:25:02,977 - ExperimentLoggers - INFO - 已记录轮次 1 before 阶段的所有评论和回复到 round_1_before_comments.json，共 32 条
2025-08-26 21:25:02,977 - ExperimentLogger - INFO - 开始记录轮次 1 before 阶段的实验指标...
2025-08-26 21:26:20,617 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的SIR指标到 round_1_before_sir_metrics.json
2025-08-26 21:26:20,618 - ExperimentLogger - INFO -   - 计算方法: 基于用户发言内容的谣言检测（符合SIR状态转换规则）
2025-08-26 21:26:20,618 - ExperimentLogger - INFO -   - 总体易感者: 94 (0.940) - 从未发布谣言内容
2025-08-26 21:26:20,618 - ExperimentLogger - INFO -   - 总体感染者: 5 (0.050) - 发布过谣言但未发布非谣言
2025-08-26 21:26:20,618 - ExperimentLogger - INFO -   - 总体康复者: 1 (0.010) - 曾经是感染者且后来发布了非谣言
2025-08-26 21:26:20,619 - ExperimentLogger - INFO -   - 知识分子群体SIR指标: 总数=0, 易感者=0(0.000), 感染者=0(0.000), 康复者=0(0.000)
2025-08-26 21:26:20,619 - ExperimentLogger - INFO -   - 普通群众群体SIR指标: 总数=100, 易感者=94(0.940), 感染者=5(0.050), 康复者=1(0.010)
2025-08-26 21:26:24,652 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的极化指数到 round_1_before_polarization_metrics.json
2025-08-26 21:26:24,652 - ExperimentLogger - INFO -   - 总体OEI指数: 0.210
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 极端观点用户: 21 (0.210)
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 温和观点用户: 2 (0.020)
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 无明确观点用户: 77 (0.770)
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 知识分子群体OEI指数: 0.000 (总数: 0, 极端: 0, 温和: 0, 无观点: 0)
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 普通群众群体OEI指数: 0.210 (总数: 100, 极端: 21, 温和: 2, 无观点: 77)
2025-08-26 21:26:28,943 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的智能体演化数据到 round_1_before_agent_evolution.json
2025-08-26 21:26:28,943 - ExperimentLogger - INFO -   - 追踪用户数: 100
2025-08-26 21:26:28,958 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的网络传播分析到 round_1_before_network_propagation.json
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 活跃用户数: 9
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 内容创作者数: 9
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 评论者数: 0
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 总帖子数: 10
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 总评论数: 32
2025-08-26 21:26:28,958 - ExperimentLogger - INFO - 轮次 1 before 阶段的实验指标记录完成
2025-08-26 21:26:28,959 - Simulator - INFO - 设置轮数信息: 当前轮次 1/40
2025-08-26 21:26:28,959 - Simulator - INFO - Starting simulation with 5 steps...
2025-08-26 21:26:28,959 - Simulator - INFO - Simulation step 1/5
2025-08-26 21:26:31,620 - Simulator - INFO - Added CREATE_POST event for user user_98 to queue. Queue size: 1
2025-08-26 21:26:31,620 - Simulator - INFO - Processing event from queue: CREATE_POST for user user_98
2025-08-26 21:26:31,620 - Engine - INFO - ----- Processing action 'CREATE_POST' for user 'user_98' -----
2025-08-26 21:26:40,913 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_98_0a64e22f).
2025-08-26 21:26:40,914 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_POST'...
2025-08-26 21:26:44,873 - src.modules.content_module - INFO - 帖子 'post_fc397c1f' 由用户 'user_98' 创建成功。
2025-08-26 21:26:44,873 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:26:44,876 - NotificationModule - INFO - Would send notification to user 'user_92': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,884 - Simulator - INFO - Added READ_POST event for user user_92 to queue. Queue size: 1
2025-08-26 21:26:44,884 - Simulator - INFO - Generated READ_POST event for user user_92 based on notification.
2025-08-26 21:26:44,884 - NotificationModule - INFO - Would send notification to user 'user_40': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,892 - Simulator - INFO - Added READ_POST event for user user_40 to queue. Queue size: 2
2025-08-26 21:26:44,892 - Simulator - INFO - Generated READ_POST event for user user_40 based on notification.
2025-08-26 21:26:44,892 - NotificationModule - INFO - Would send notification to user 'user_75': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,899 - Simulator - INFO - Added READ_POST event for user user_75 to queue. Queue size: 3
2025-08-26 21:26:44,900 - Simulator - INFO - Generated READ_POST event for user user_75 based on notification.
2025-08-26 21:26:44,900 - NotificationModule - INFO - Would send notification to user 'user_43': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,907 - Simulator - INFO - Added READ_POST event for user user_43 to queue. Queue size: 4
2025-08-26 21:26:44,908 - Simulator - INFO - Generated READ_POST event for user user_43 based on notification.
2025-08-26 21:26:44,908 - NotificationModule - INFO - Would send notification to user 'user_24': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,915 - Simulator - INFO - Added READ_POST event for user user_24 to queue. Queue size: 5
2025-08-26 21:26:44,916 - Simulator - INFO - Generated READ_POST event for user user_24 based on notification.
2025-08-26 21:26:44,916 - NotificationModule - INFO - Would send notification to user 'user_90': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,923 - Simulator - INFO - Added READ_POST event for user user_90 to queue. Queue size: 6
2025-08-26 21:26:44,923 - Simulator - INFO - Generated READ_POST event for user user_90 based on notification.
2025-08-26 21:26:44,924 - NotificationModule - INFO - Would send notification to user 'user_61': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,931 - Simulator - INFO - Added READ_POST event for user user_61 to queue. Queue size: 7
2025-08-26 21:26:44,931 - Simulator - INFO - Generated READ_POST event for user user_61 based on notification.
2025-08-26 21:26:44,932 - NotificationModule - INFO - Would send notification to user 'user_34': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,939 - Simulator - INFO - Added READ_POST event for user user_34 to queue. Queue size: 8
2025-08-26 21:26:44,939 - Simulator - INFO - Generated READ_POST event for user user_34 based on notification.
2025-08-26 21:26:44,939 - NotificationModule - INFO - Would send notification to user 'user_53': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,947 - Simulator - INFO - Added READ_POST event for user user_53 to queue. Queue size: 9
2025-08-26 21:26:44,947 - Simulator - INFO - Generated READ_POST event for user user_53 based on notification.
2025-08-26 21:26:44,947 - NotificationModule - INFO - Would send notification to user 'user_100': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,955 - Simulator - INFO - Added READ_POST event for user user_100 to queue. Queue size: 10
2025-08-26 21:26:44,955 - Simulator - INFO - Generated READ_POST event for user user_100 based on notification.
2025-08-26 21:26:44,955 - Engine - INFO - Sent new post notification to 10 followers.
2025-08-26 21:26:44,955 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_98'...
2025-08-26 21:26:44,956 - src.modules.belief_module - INFO - 开始处理用户 'user_98' 的认知流程...
2025-08-26 21:26:44,970 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:26:44,970 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:26:44,970 - src.modules.belief_module - INFO - 用户 'user_98' 的短期记忆中未找到显著议题簇
2025-08-26 21:26:44,971 - Engine - INFO - Cognitive processing for user 'user_98' finished.
2025-08-26 21:26:44,971 - Engine - INFO - ----- Action 'CREATE_POST' for user 'user_98' processed successfully. -----
2025-08-26 21:26:44,971 - Simulator - INFO - Processing event from queue: READ_POST for user user_92
2025-08-26 21:26:44,972 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_92' -----
2025-08-26 21:26:52,187 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_92_abb65c28).
2025-08-26 21:26:52,187 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:26:54,866 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:26:54,866 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:26:54,867 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:26:54,867 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_92'...
2025-08-26 21:26:54,867 - src.modules.belief_module - INFO - 开始处理用户 'user_92' 的认知流程...
2025-08-26 21:26:54,881 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:26:54,882 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:26:54,882 - src.modules.belief_module - INFO - 用户 'user_92' 的短期记忆中未找到显著议题簇
2025-08-26 21:26:54,882 - Engine - INFO - Cognitive processing for user 'user_92' finished.
2025-08-26 21:26:54,882 - Engine - INFO - ----- Action 'READ_POST' for user 'user_92' processed successfully. -----
2025-08-26 21:26:54,883 - Simulator - INFO - Processing event from queue: READ_POST for user user_40
2025-08-26 21:26:54,883 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_40' -----
MemoryModule initialized.

【模拟步骤】选择用户: user_98 执行操作 (步骤 1/5, 轮次 1/40)
【用户行为】用户 user_98 选择了 CREATE_POST 操作，内容："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: CREATE_POST (用户 user_98), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_POST (用户 user_98)，发布内容："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【引擎】开始处理用户 user_98 的 CREATE_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_98'...
MemoryModule: Successfully created memory 'mem_user_98_0a64e22f'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_98_0a64e22f)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_98 的 CREATE_POST 操作是否需要发送通知...
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_92
【通知事件】生成通知事件: 用户 user_92 接收 READ_POST 通知
【通知事件】为用户 user_92 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_92), 队列大小: 1
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_40
【通知事件】生成通知事件: 用户 user_40 接收 READ_POST 通知
【通知事件】为用户 user_40 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_40), 队列大小: 2
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_75
【通知事件】生成通知事件: 用户 user_75 接收 READ_POST 通知
【通知事件】为用户 user_75 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_75), 队列大小: 3
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_43
【通知事件】生成通知事件: 用户 user_43 接收 READ_POST 通知
【通知事件】为用户 user_43 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_43), 队列大小: 4
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_24
【通知事件】生成通知事件: 用户 user_24 接收 READ_POST 通知
【通知事件】为用户 user_24 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_24), 队列大小: 5
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_90
【通知事件】生成通知事件: 用户 user_90 接收 READ_POST 通知
【通知事件】为用户 user_90 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_90), 队列大小: 6
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_61
【通知事件】生成通知事件: 用户 user_61 接收 READ_POST 通知
【通知事件】为用户 user_61 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_61), 队列大小: 7
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_34
【通知事件】生成通知事件: 用户 user_34 接收 READ_POST 通知
【通知事件】为用户 user_34 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_34), 队列大小: 8
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_53
【通知事件】生成通知事件: 用户 user_53 接收 READ_POST 通知
【通知事件】为用户 user_53 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_53), 队列大小: 9
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_100
【通知事件】生成通知事件: 用户 user_100 接收 READ_POST 通知
【通知事件】为用户 user_100 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_100), 队列大小: 10
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_98 的认知处理...

【认知处理】开始处理用户 user_98 的认知流程
【阶段1+2】获取用户 user_98 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_98'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_98 的认知处理完成
【引擎】用户 user_98 的 CREATE_POST 操作处理成功
【处理成功】事件处理成功: CREATE_POST (用户 user_98)
【处理事件】从队列中处理事件: READ_POST (用户 user_92)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_92 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_92'...
MemoryModule: Successfully created memory 'mem_user_92_abb65c28'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_92_abb65c28)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_92 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_92 的认知处理...

【认知处理】开始处理用户 user_92 的认知流程
【阶段1+2】获取用户 user_92 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_92'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_92 的认知处理完成
【引擎】用户 user_92 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_92)
【处理事件】从队列中处理事件: READ_POST (用户 user_40)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_40 的 READ_POST 操作
2025-08-26 21:27:02,005 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_40_8b457d25).
2025-08-26 21:27:02,005 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:04,875 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:04,876 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:04,876 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:04,876 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_40'...
2025-08-26 21:27:04,876 - src.modules.belief_module - INFO - 开始处理用户 'user_40' 的认知流程...
2025-08-26 21:27:04,891 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:04,891 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:04,891 - src.modules.belief_module - INFO - 用户 'user_40' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:04,891 - Engine - INFO - Cognitive processing for user 'user_40' finished.
2025-08-26 21:27:04,891 - Engine - INFO - ----- Action 'READ_POST' for user 'user_40' processed successfully. -----
2025-08-26 21:27:04,892 - Simulator - INFO - Processing event from queue: READ_POST for user user_75
2025-08-26 21:27:04,892 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_75' -----
2025-08-26 21:27:12,322 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_75_0c19ce76).
2025-08-26 21:27:12,322 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:15,335 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:15,335 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:15,335 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:15,336 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_75'...
2025-08-26 21:27:15,336 - src.modules.belief_module - INFO - 开始处理用户 'user_75' 的认知流程...
2025-08-26 21:27:15,350 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:15,350 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:15,350 - src.modules.belief_module - INFO - 用户 'user_75' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:15,351 - Engine - INFO - Cognitive processing for user 'user_75' finished.
2025-08-26 21:27:15,351 - Engine - INFO - ----- Action 'READ_POST' for user 'user_75' processed successfully. -----
2025-08-26 21:27:15,351 - Simulator - INFO - Processing event from queue: READ_POST for user user_43
2025-08-26 21:27:15,351 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_43' -----
2025-08-26 21:27:22,731 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_43_cbb9b8d5).
2025-08-26 21:27:22,732 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:25,729 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:25,730 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:25,730 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:25,730 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_43'...
2025-08-26 21:27:25,730 - src.modules.belief_module - INFO - 开始处理用户 'user_43' 的认知流程...
2025-08-26 21:27:25,745 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:25,745 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:25,745 - src.modules.belief_module - INFO - 用户 'user_43' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:25,745 - Engine - INFO - Cognitive processing for user 'user_43' finished.
2025-08-26 21:27:25,745 - Engine - INFO - ----- Action 'READ_POST' for user 'user_43' processed successfully. -----
2025-08-26 21:27:25,746 - Simulator - INFO - Processing event from queue: READ_POST for user user_24
2025-08-26 21:27:25,746 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_24' -----
2025-08-26 21:27:32,902 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_24_e5f78abf).
2025-08-26 21:27:32,902 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:35,782 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:35,782 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:35,782 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:35,782 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_24'...
2025-08-26 21:27:35,782 - src.modules.belief_module - INFO - 开始处理用户 'user_24' 的认知流程...
2025-08-26 21:27:35,797 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:35,797 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:35,797 - src.modules.belief_module - INFO - 用户 'user_24' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:35,798 - Engine - INFO - Cognitive processing for user 'user_24' finished.
2025-08-26 21:27:35,798 - Engine - INFO - ----- Action 'READ_POST' for user 'user_24' processed successfully. -----
2025-08-26 21:27:35,798 - Simulator - INFO - Processing event from queue: READ_POST for user user_90
2025-08-26 21:27:35,798 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_90' -----
2025-08-26 21:27:43,050 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_90_cdf9f8e9).
2025-08-26 21:27:43,050 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:45,927 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:45,928 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:45,928 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:45,928 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_90'...
2025-08-26 21:27:45,928 - src.modules.belief_module - INFO - 开始处理用户 'user_90' 的认知流程...
2025-08-26 21:27:45,943 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:45,943 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:45,943 - src.modules.belief_module - INFO - 用户 'user_90' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:45,943 - Engine - INFO - Cognitive processing for user 'user_90' finished.
2025-08-26 21:27:45,943 - Engine - INFO - ----- Action 'READ_POST' for user 'user_90' processed successfully. -----
2025-08-26 21:27:45,944 - Simulator - INFO - Processing event from queue: READ_POST for user user_61
2025-08-26 21:27:45,944 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_61' -----
2025-08-26 21:27:53,107 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_61_8bf1f80e).
2025-08-26 21:27:53,107 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:55,745 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:55,745 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:55,745 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:55,746 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_61'...
2025-08-26 21:27:55,746 - src.modules.belief_module - INFO - 开始处理用户 'user_61' 的认知流程...
2025-08-26 21:27:55,761 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:55,761 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:55,761 - src.modules.belief_module - INFO - 用户 'user_61' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:55,762 - Engine - INFO - Cognitive processing for user 'user_61' finished.
2025-08-26 21:27:55,762 - Engine - INFO - ----- Action 'READ_POST' for user 'user_61' processed successfully. -----
2025-08-26 21:27:55,762 - Simulator - INFO - Processing event from queue: READ_POST for user user_34
2025-08-26 21:27:55,762 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_34' -----
2025-08-26 21:28:02,500 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_34_5b82c743).
2025-08-26 21:28:02,501 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:28:05,538 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_40'...
MemoryModule: Successfully created memory 'mem_user_40_8b457d25'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_40_8b457d25)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_40 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_40 的认知处理...

【认知处理】开始处理用户 user_40 的认知流程
【阶段1+2】获取用户 user_40 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_40'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_40 的认知处理完成
【引擎】用户 user_40 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_40)
【处理事件】从队列中处理事件: READ_POST (用户 user_75)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_75 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_75'...
MemoryModule: Successfully created memory 'mem_user_75_0c19ce76'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_75_0c19ce76)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_75 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_75 的认知处理...

【认知处理】开始处理用户 user_75 的认知流程
【阶段1+2】获取用户 user_75 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_75'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_75 的认知处理完成
【引擎】用户 user_75 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_75)
【处理事件】从队列中处理事件: READ_POST (用户 user_43)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_43 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_43'...
MemoryModule: Successfully created memory 'mem_user_43_cbb9b8d5'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_43_cbb9b8d5)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_43 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_43 的认知处理...

【认知处理】开始处理用户 user_43 的认知流程
【阶段1+2】获取用户 user_43 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_43'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_43 的认知处理完成
【引擎】用户 user_43 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_43)
【处理事件】从队列中处理事件: READ_POST (用户 user_24)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_24 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_24'...
MemoryModule: Successfully created memory 'mem_user_24_e5f78abf'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_24_e5f78abf)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_24 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_24 的认知处理...

【认知处理】开始处理用户 user_24 的认知流程
【阶段1+2】获取用户 user_24 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_24'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_24 的认知处理完成
【引擎】用户 user_24 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_24)
【处理事件】从队列中处理事件: READ_POST (用户 user_90)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_90 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_90'...
MemoryModule: Successfully created memory 'mem_user_90_cdf9f8e9'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_90_cdf9f8e9)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_90 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_90 的认知处理...

【认知处理】开始处理用户 user_90 的认知流程
【阶段1+2】获取用户 user_90 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_90'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_90 的认知处理完成
【引擎】用户 user_90 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_90)
【处理事件】从队列中处理事件: READ_POST (用户 user_61)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_61 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_61'...
MemoryModule: Successfully created memory 'mem_user_61_8bf1f80e'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_61_8bf1f80e)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_61 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_61 的认知处理...

【认知处理】开始处理用户 user_61 的认知流程
【阶段1+2】获取用户 user_61 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_61'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_61 的认知处理完成
【引擎】用户 user_61 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_61)
【处理事件】从队列中处理事件: READ_POST (用户 user_34)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_34 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_34'...
MemoryModule: Successfully created memory 'mem_user_34_5b82c743'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_34_5b82c743)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
2025-08-26 21:28:05,539 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:28:05,539 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:28:05,539 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_34'...
2025-08-26 21:28:05,539 - src.modules.belief_module - INFO - 开始处理用户 'user_34' 的认知流程...
2025-08-26 21:28:05,554 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:28:05,554 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:28:05,554 - src.modules.belief_module - INFO - 用户 'user_34' 的短期记忆中未找到显著议题簇
2025-08-26 21:28:05,555 - Engine - INFO - Cognitive processing for user 'user_34' finished.
2025-08-26 21:28:05,555 - Engine - INFO - ----- Action 'READ_POST' for user 'user_34' processed successfully. -----
2025-08-26 21:28:05,555 - Simulator - INFO - Processing event from queue: READ_POST for user user_53
2025-08-26 21:28:05,555 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_53' -----
2025-08-26 21:28:13,052 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_53_dd8f6ac8).
2025-08-26 21:28:13,052 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:28:15,931 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:28:15,932 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:28:15,932 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:28:15,932 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_53'...
2025-08-26 21:28:15,932 - src.modules.belief_module - INFO - 开始处理用户 'user_53' 的认知流程...
2025-08-26 21:28:15,947 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:28:15,947 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:28:15,947 - src.modules.belief_module - INFO - 用户 'user_53' 的短期记忆中未找到显著议题簇
2025-08-26 21:28:15,947 - Engine - INFO - Cognitive processing for user 'user_53' finished.
2025-08-26 21:28:15,947 - Engine - INFO - ----- Action 'READ_POST' for user 'user_53' processed successfully. -----
2025-08-26 21:28:15,948 - Simulator - INFO - Processing event from queue: READ_POST for user user_100
2025-08-26 21:28:15,948 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_100' -----
2025-08-26 21:28:23,312 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_100_4366e4a8).
2025-08-26 21:28:23,312 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:28:28,738 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:28:28,738 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:28:28,738 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:28:28,738 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_100'...
2025-08-26 21:28:28,738 - src.modules.belief_module - INFO - 开始处理用户 'user_100' 的认知流程...
2025-08-26 21:28:28,754 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:28:28,754 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:28:28,754 - src.modules.belief_module - INFO - 用户 'user_100' 的短期记忆中未找到显著议题簇
2025-08-26 21:28:28,754 - Engine - INFO - Cognitive processing for user 'user_100' finished.
2025-08-26 21:28:28,754 - Engine - INFO - ----- Action 'READ_POST' for user 'user_100' processed successfully. -----
2025-08-26 21:28:28,755 - Simulator - INFO - Simulation step completed for user user_98.
2025-08-26 21:28:28,755 - Simulator - INFO - Simulation step 2/5
2025-08-26 21:28:30,735 - Simulator - INFO - Added CREATE_COMMENT event for user user_62 to queue. Queue size: 1
2025-08-26 21:28:30,735 - Simulator - INFO - Processing event from queue: CREATE_COMMENT for user user_62
2025-08-26 21:28:30,735 - Engine - INFO - ----- Processing action 'CREATE_COMMENT' for user 'user_62' -----
2025-08-26 21:28:37,102 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_62_84249dba).
2025-08-26 21:28:37,103 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_COMMENT'...
2025-08-26 21:28:44,545 - src.modules.content_module - INFO - Incremented comment count for post 'post_8'.
2025-08-26 21:28:49,846 - src.modules.agent_module - INFO - 用户 'user_62' 正在 关注 帖子 'post_8'...
2025-08-26 21:28:54,634 - src.modules.agent_module - INFO - 成功处理用户 'user_62' 关注 帖子 'post_8' 的操作。
2025-08-26 21:28:54,634 - src.modules.content_module - INFO - User 'user_62' successfully followed post 'post_8'.
2025-08-26 21:28:54,634 - src.modules.content_module - INFO - Added comment 'comment_c8687405' to post 'post_8' by user 'user_62'
2025-08-26 21:28:54,635 - src.modules.agent_module - INFO - 根据ID推断，正在为用户 'user_62' 的 'comments' 列表添加 ID 'comment_c8687405'...
2025-08-26 21:28:57,615 - src.modules.agent_module - INFO - 成功为用户 'user_62' 添加活动。
2025-08-26 21:28:57,615 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:28:57,618 - NotificationModule - INFO - Would send notification to user 'user_29': Your post 'post_8' was commented on by user 'user_62'.
2025-08-26 21:28:57,626 - Simulator - INFO - Added READ_POST event for user user_29 to queue. Queue size: 1
2025-08-26 21:28:57,626 - Simulator - INFO - Generated READ_POST event for user user_29 based on notification.
2025-08-26 21:28:57,626 - Engine - INFO - Comment notification sent to post author.
2025-08-26 21:28:57,631 - Engine - INFO - Sent new comment notification to 1 post followers.
2025-08-26 21:28:57,632 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_62'...
2025-08-26 21:28:57,632 - src.modules.belief_module - INFO - 开始处理用户 'user_62' 的认知流程...
2025-08-26 21:28:57,646 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:28:57,646 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:28:57,646 - src.modules.belief_module - INFO - 用户 'user_62' 的短期记忆中未找到显著议题簇
2025-08-26 21:28:57,647 - Engine - INFO - Cognitive processing for user 'user_62' finished.
2025-08-26 21:28:57,647 - Engine - INFO - ----- Action 'CREATE_COMMENT' for user 'user_62' processed successfully. -----
2025-08-26 21:28:57,647 - Simulator - INFO - Processing event from queue: READ_POST for user user_29
2025-08-26 21:28:57,648 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_29' -----
2025-08-26 21:29:04,159 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_29_7dc355ed).
2025-08-26 21:29:04,159 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:29:07,632 - src.modules.content_module - INFO - Incremented view count for post 'post_8'.
2025-08-26 21:29:07,632 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:29:07,632 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:29:07,632 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_29'...
2025-08-26 21:29:07,632 - src.modules.belief_module - INFO - 开始处理用户 'user_29' 的认知流程...
2025-08-26 21:29:07,647 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:29:07,647 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:29:07,647 - src.modules.belief_module - INFO - 用户 'user_29' 的短期记忆中未找到显著议题簇
2025-08-26 21:29:07,647 - Engine - INFO - Cognitive processing for user 'user_29' finished.
2025-08-26 21:29:07,648 - Engine - INFO - ----- Action 'READ_POST' for user 'user_29' processed successfully. -----
2025-08-26 21:29:07,648 - Simulator - INFO - Simulation step completed for user user_62.
2025-08-26 21:29:07,648 - Simulator - INFO - Simulation step 3/5
2025-08-26 21:29:07,929 - Simulator - INFO - Added READ_POST event for user user_13 to queue. Queue size: 1
2025-08-26 21:29:07,930 - Simulator - INFO - Processing event from queue: READ_POST for user user_13
2025-08-26 21:29:07,930 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_13' -----
2025-08-26 21:29:14,780 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_13_f311c675).
2025-08-26 21:29:14,780 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:29:17,709 - src.modules.content_module - INFO - Incremented view count for post 'post_4'.
2025-08-26 21:29:17,709 - Engine - INFO - Step 3/4: Checking for notifications...
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_34 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_34 的认知处理...

【认知处理】开始处理用户 user_34 的认知流程
【阶段1+2】获取用户 user_34 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_34'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_34 的认知处理完成
【引擎】用户 user_34 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_34)
【处理事件】从队列中处理事件: READ_POST (用户 user_53)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_53 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_53'...
MemoryModule: Successfully created memory 'mem_user_53_dd8f6ac8'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_53_dd8f6ac8)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_53 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_53 的认知处理...

【认知处理】开始处理用户 user_53 的认知流程
【阶段1+2】获取用户 user_53 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_53'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_53 的认知处理完成
【引擎】用户 user_53 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_53)
【处理事件】从队列中处理事件: READ_POST (用户 user_100)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_100 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_100'...
MemoryModule: Successfully created memory 'mem_user_100_4366e4a8'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_100_4366e4a8)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_100 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_100 的认知处理...

【认知处理】开始处理用户 user_100 的认知流程
【阶段1+2】获取用户 user_100 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_100'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_100 的认知处理完成
【引擎】用户 user_100 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_100)

【模拟步骤】选择用户: user_62 执行操作 (步骤 2/5, 轮次 1/40)
【用户行为】用户 user_62 选择了 CREATE_COMMENT 操作，评论帖子 post_8："这帖子吓人是吓人，但别慌。要是真有公司拿咱们的邮件偷偷训练AI，那性质就变了。我就不信他们能一直瞒着..."
【事件队列】添加事件: CREATE_COMMENT (用户 user_62), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_COMMENT (用户 user_62)，评论帖子 post_8："这帖子吓人是吓人，但别慌。要是真有公司拿咱们的邮件偷偷训练AI，那性质就变了。我就不信他们能一直瞒着..."
【引擎】开始处理用户 user_62 的 CREATE_COMMENT 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_62'...
MemoryModule: Successfully created memory 'mem_user_62_84249dba'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_62_84249dba)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_62 的 CREATE_COMMENT 操作是否需要发送通知...
【通知】发送评论通知给帖子作者 user_29，评论内容："这帖子吓人是吓人，但别慌。要是真有公司拿咱们的邮件偷偷训练AI，那性质就变了。我就不信他们能一直瞒着..."
【通知模块】用户 user_62 评论了帖子，发送通知给帖子作者 user_29
【通知事件】生成通知事件: 用户 user_29 接收 READ_POST 通知
【通知事件】为用户 user_29 生成浏览事件，浏览帖子 post_8："真相令人作呕！科技巨头秘密开发的“守望者”AI，其强大的能力从何而来？答案是我们！我们每天发送的邮件..."
【事件队列】添加事件: READ_POST (用户 user_29), 队列大小: 1
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_62 的认知处理...

【认知处理】开始处理用户 user_62 的认知流程
【阶段1+2】获取用户 user_62 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_62'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_62 的认知处理完成
【引擎】用户 user_62 的 CREATE_COMMENT 操作处理成功
【处理成功】事件处理成功: CREATE_COMMENT (用户 user_62)
【处理事件】从队列中处理事件: READ_POST (用户 user_29)，浏览帖子 post_8："评论内容: 这帖子吓人是吓人，但别慌。要是真有公司拿咱们的邮件偷偷训练AI，那性质就变了。我就不信他..."
【引擎】开始处理用户 user_29 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_29'...
MemoryModule: Successfully created memory 'mem_user_29_7dc355ed'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_29_7dc355ed)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_29 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_29 的认知处理...

【认知处理】开始处理用户 user_29 的认知流程
【阶段1+2】获取用户 user_29 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_29'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_29 的认知处理完成
【引擎】用户 user_29 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_29)

【模拟步骤】选择用户: user_13 执行操作 (步骤 3/5, 轮次 1/40)
【用户行为】用户 user_13 选择了 READ_POST 操作，浏览帖子 post_4："细思极恐！名为“守望者”的AI项目一旦成功，意味着未来你的老板将是一个没有任何感情的程序。它根据数据..."
【事件队列】添加事件: READ_POST (用户 user_13), 队列大小: 1
【处理事件】从队列中处理事件: READ_POST (用户 user_13)，浏览帖子 post_4："细思极恐！名为“守望者”的AI项目一旦成功，意味着未来你的老板将是一个没有任何感情的程序。它根据数据..."
【引擎】开始处理用户 user_13 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_13'...
MemoryModule: Successfully created memory 'mem_user_13_f311c675'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_13_f311c675)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
2025-08-26 21:29:17,710 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:29:17,710 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_13'...
2025-08-26 21:29:17,710 - src.modules.belief_module - INFO - 开始处理用户 'user_13' 的认知流程...
2025-08-26 21:29:17,725 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:29:17,725 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:29:17,726 - src.modules.belief_module - INFO - 用户 'user_13' 的短期记忆中未找到显著议题簇
2025-08-26 21:29:17,726 - Engine - INFO - Cognitive processing for user 'user_13' finished.
2025-08-26 21:29:17,726 - Engine - INFO - ----- Action 'READ_POST' for user 'user_13' processed successfully. -----
2025-08-26 21:29:17,727 - Simulator - INFO - Simulation step completed for user user_13.
2025-08-26 21:29:17,727 - Simulator - INFO - Simulation step 4/5
2025-08-26 21:29:17,998 - Simulator - INFO - Added READ_POST event for user user_63 to queue. Queue size: 1
2025-08-26 21:29:17,998 - Simulator - INFO - Processing event from queue: READ_POST for user user_63
2025-08-26 21:29:17,999 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_63' -----
2025-08-26 21:29:24,450 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_63_b39b085f).
2025-08-26 21:29:24,450 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:29:27,564 - src.modules.content_module - INFO - Incremented view count for post 'post_1'.
2025-08-26 21:29:27,565 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:29:27,565 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:29:27,565 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_63'...
2025-08-26 21:29:27,565 - src.modules.belief_module - INFO - 开始处理用户 'user_63' 的认知流程...
2025-08-26 21:29:27,579 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:29:27,580 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:29:27,580 - src.modules.belief_module - INFO - 用户 'user_63' 的短期记忆中未找到显著议题簇
2025-08-26 21:29:27,580 - Engine - INFO - Cognitive processing for user 'user_63' finished.
2025-08-26 21:29:27,580 - Engine - INFO - ----- Action 'READ_POST' for user 'user_63' processed successfully. -----
2025-08-26 21:29:27,581 - Simulator - INFO - Simulation step completed for user user_63.
2025-08-26 21:29:27,581 - Simulator - INFO - Simulation step 5/5
2025-08-26 21:29:30,409 - Simulator - INFO - Added CREATE_POST event for user user_53 to queue. Queue size: 1
2025-08-26 21:29:30,409 - Simulator - INFO - Processing event from queue: CREATE_POST for user user_53
2025-08-26 21:29:30,409 - Engine - INFO - ----- Processing action 'CREATE_POST' for user 'user_53' -----
2025-08-26 21:29:37,685 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_53_62674229).
2025-08-26 21:29:37,686 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_POST'...
2025-08-26 21:29:40,339 - src.modules.content_module - INFO - 帖子 'post_632c077b' 由用户 'user_53' 创建成功。
2025-08-26 21:29:40,339 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:29:40,342 - NotificationModule - INFO - Would send notification to user 'user_73': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,350 - Simulator - INFO - Added READ_POST event for user user_73 to queue. Queue size: 1
2025-08-26 21:29:40,350 - Simulator - INFO - Generated READ_POST event for user user_73 based on notification.
2025-08-26 21:29:40,350 - NotificationModule - INFO - Would send notification to user 'user_49': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,358 - Simulator - INFO - Added READ_POST event for user user_49 to queue. Queue size: 2
2025-08-26 21:29:40,358 - Simulator - INFO - Generated READ_POST event for user user_49 based on notification.
2025-08-26 21:29:40,358 - NotificationModule - INFO - Would send notification to user 'user_91': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,366 - Simulator - INFO - Added READ_POST event for user user_91 to queue. Queue size: 3
2025-08-26 21:29:40,366 - Simulator - INFO - Generated READ_POST event for user user_91 based on notification.
2025-08-26 21:29:40,366 - NotificationModule - INFO - Would send notification to user 'user_54': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,373 - Simulator - INFO - Added READ_POST event for user user_54 to queue. Queue size: 4
2025-08-26 21:29:40,374 - Simulator - INFO - Generated READ_POST event for user user_54 based on notification.
2025-08-26 21:29:40,374 - NotificationModule - INFO - Would send notification to user 'user_24': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,381 - Simulator - INFO - Added READ_POST event for user user_24 to queue. Queue size: 5
2025-08-26 21:29:40,382 - Simulator - INFO - Generated READ_POST event for user user_24 based on notification.
2025-08-26 21:29:40,382 - NotificationModule - INFO - Would send notification to user 'user_21': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,389 - Simulator - INFO - Added READ_POST event for user user_21 to queue. Queue size: 6
2025-08-26 21:29:40,389 - Simulator - INFO - Generated READ_POST event for user user_21 based on notification.
2025-08-26 21:29:40,390 - NotificationModule - INFO - Would send notification to user 'user_20': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,397 - Simulator - INFO - Added READ_POST event for user user_20 to queue. Queue size: 7
2025-08-26 21:29:40,397 - Simulator - INFO - Generated READ_POST event for user user_20 based on notification.
2025-08-26 21:29:40,398 - NotificationModule - INFO - Would send notification to user 'user_19': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,405 - Simulator - INFO - Added READ_POST event for user user_19 to queue. Queue size: 8
2025-08-26 21:29:40,405 - Simulator - INFO - Generated READ_POST event for user user_19 based on notification.
2025-08-26 21:29:40,405 - NotificationModule - INFO - Would send notification to user 'user_96': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,413 - Simulator - INFO - Added READ_POST event for user user_96 to queue. Queue size: 9
2025-08-26 21:29:40,413 - Simulator - INFO - Generated READ_POST event for user user_96 based on notification.
2025-08-26 21:29:40,414 - NotificationModule - INFO - Would send notification to user 'user_26': User 'user_53' you follow has published a new post 'post_632c077b'.
【通知】检查用户 user_13 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_13 的认知处理...

【认知处理】开始处理用户 user_13 的认知流程
【阶段1+2】获取用户 user_13 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_13'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_13 的认知处理完成
【引擎】用户 user_13 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_13)

【模拟步骤】选择用户: user_63 执行操作 (步骤 4/5, 轮次 1/40)
【用户行为】用户 user_63 选择了 READ_POST 操作，浏览帖子 post_1："惊天内幕！某顶级科技公司秘密开发名为“守望者”的超级AI，能像“天网”一样分析公司所有数据，从财务到..."
【事件队列】添加事件: READ_POST (用户 user_63), 队列大小: 1
【处理事件】从队列中处理事件: READ_POST (用户 user_63)，浏览帖子 post_1："惊天内幕！某顶级科技公司秘密开发名为“守望者”的超级AI，能像“天网”一样分析公司所有数据，从财务到..."
【引擎】开始处理用户 user_63 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_63'...
MemoryModule: Successfully created memory 'mem_user_63_b39b085f'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_63_b39b085f)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_63 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_63 的认知处理...

【认知处理】开始处理用户 user_63 的认知流程
【阶段1+2】获取用户 user_63 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_63'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_63 的认知处理完成
【引擎】用户 user_63 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_63)

【模拟步骤】选择用户: user_53 执行操作 (步骤 5/5, 轮次 1/40)
【用户行为】用户 user_53 选择了 CREATE_POST 操作，内容："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: CREATE_POST (用户 user_53), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_POST (用户 user_53)，发布内容："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【引擎】开始处理用户 user_53 的 CREATE_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_53'...
MemoryModule: Successfully created memory 'mem_user_53_62674229'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_53_62674229)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_53 的 CREATE_POST 操作是否需要发送通知...
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_73
【通知事件】生成通知事件: 用户 user_73 接收 READ_POST 通知
【通知事件】为用户 user_73 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_73), 队列大小: 1
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_49
【通知事件】生成通知事件: 用户 user_49 接收 READ_POST 通知
【通知事件】为用户 user_49 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_49), 队列大小: 2
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_91
【通知事件】生成通知事件: 用户 user_91 接收 READ_POST 通知
【通知事件】为用户 user_91 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_91), 队列大小: 3
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_54
【通知事件】生成通知事件: 用户 user_54 接收 READ_POST 通知
【通知事件】为用户 user_54 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_54), 队列大小: 4
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_24
【通知事件】生成通知事件: 用户 user_24 接收 READ_POST 通知
【通知事件】为用户 user_24 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_24), 队列大小: 5
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_21
【通知事件】生成通知事件: 用户 user_21 接收 READ_POST 通知
【通知事件】为用户 user_21 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_21), 队列大小: 6
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_20
【通知事件】生成通知事件: 用户 user_20 接收 READ_POST 通知
【通知事件】为用户 user_20 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_20), 队列大小: 7
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_19
【通知事件】生成通知事件: 用户 user_19 接收 READ_POST 通知
【通知事件】为用户 user_19 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_19), 队列大小: 8
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_96
【通知事件】生成通知事件: 用户 user_96 接收 READ_POST 通知
【通知事件】为用户 user_96 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_96), 队列大小: 9
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_26
【通知事件】生成通知事件: 用户 user_26 接收 READ_POST 通知
2025-08-26 21:29:40,421 - Simulator - INFO - Added READ_POST event for user user_26 to queue. Queue size: 10
2025-08-26 21:29:40,421 - Simulator - INFO - Generated READ_POST event for user user_26 based on notification.
2025-08-26 21:29:40,422 - NotificationModule - INFO - Would send notification to user 'user_88': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,429 - Simulator - INFO - Added READ_POST event for user user_88 to queue. Queue size: 11
2025-08-26 21:29:40,429 - Simulator - INFO - Generated READ_POST event for user user_88 based on notification.
2025-08-26 21:29:40,429 - NotificationModule - INFO - Would send notification to user 'user_77': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,437 - Simulator - INFO - Added READ_POST event for user user_77 to queue. Queue size: 12
2025-08-26 21:29:40,437 - Simulator - INFO - Generated READ_POST event for user user_77 based on notification.
2025-08-26 21:29:40,437 - NotificationModule - INFO - Would send notification to user 'user_11': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,445 - Simulator - INFO - Added READ_POST event for user user_11 to queue. Queue size: 13
2025-08-26 21:29:40,445 - Simulator - INFO - Generated READ_POST event for user user_11 based on notification.
2025-08-26 21:29:40,446 - NotificationModule - INFO - Would send notification to user 'user_71': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,453 - Simulator - INFO - Added READ_POST event for user user_71 to queue. Queue size: 14
2025-08-26 21:29:40,453 - Simulator - INFO - Generated READ_POST event for user user_71 based on notification.
2025-08-26 21:29:40,454 - NotificationModule - INFO - Would send notification to user 'user_12': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,461 - Simulator - INFO - Added READ_POST event for user user_12 to queue. Queue size: 15
2025-08-26 21:29:40,461 - Simulator - INFO - Generated READ_POST event for user user_12 based on notification.
2025-08-26 21:29:40,462 - Engine - INFO - Sent new post notification to 15 followers.
2025-08-26 21:29:40,462 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_53'...
2025-08-26 21:29:40,462 - src.modules.belief_module - INFO - 开始处理用户 'user_53' 的认知流程...
2025-08-26 21:29:40,486 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:29:40,487 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:29:40,492 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:29:40,492 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_81621c53' 更新或创建信念...
2025-08-26 21:29:40,580 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 1), 最大相似度: 0.3387
2025-08-26 21:29:40,580 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756214187004530'
2025-08-26 21:29:40,581 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近跟家人聊结婚和分家产的事，突然觉得以前啥都摆在桌上说的那套，现在可能不太安全了。爸妈那辈人习惯把户口本、房产证、银行流水都拿出来聊，可现在这些信息要是被泄露，后果可不止闹矛盾这么简单。我看到个例子，有人婚前聊天记录被拿去当财产分割的证据，吓一跳。现在结婚讲信任，但信任也得有数据安全做底子。我劝家里人，重要的财务和身份信息别随便拍照发群里，也别随便给那些“家庭管理”APP授权。不是不信任人，是数据一出去就收不回来了。保护隐私不是冷淡，其实是对家人更负责。'
2025-08-26 21:29:40,581 - src.modules.belief_module - INFO - embedding相似度 (0.3387) 低于阈值 (0.9)，创建节点
2025-08-26 21:29:40,581 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:29:40,581 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_81621c53' 的证据（多线程模式）...
2025-08-26 21:29:45,692 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.6907, 反对权重: 0.0000
2025-08-26 21:29:45,693 - src.modules.belief_module - INFO - 正在为用户 'user_53' 创建新信念: '最近跟家人聊结婚和分家产的事，突然觉得以前啥都摆在桌上说的那...'
2025-08-26 21:29:50,595 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:29:50,595 - src.modules.belief_module - INFO - 正在为信念 'belief_user_53_c2be104d' 建立关系网络（多线程模式）...
2025-08-26 21:29:50,611 - src.modules.belief_module - INFO - 找到 4 个现有信念，开始多线程处理...
2025-08-26 21:30:01,073 - src.modules.belief_module - INFO - 建立关系 'belief_user_53_c2be104d' -反驳/diminishes-> 'belief_1756214187004530' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:30:06,019 - src.modules.belief_module - INFO - 建立关系 'belief_1756214187004530' -支持/is_example_of-> 'belief_user_53_c2be104d' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:30:09,551 - src.modules.belief_module - INFO - 建立关系 'belief_1756214187004531' -反驳/diminishes-> 'belief_user_53_c2be104d' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:30:12,775 - src.modules.belief_module - INFO - 为信念 'belief_user_53_c2be104d' 建立了 1 个关系（多线程模式）
2025-08-26 21:30:12,775 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_53_c2be104d':
2025-08-26 21:30:12,775 - src.modules.belief_module - INFO -   - 真实度: 0.6907
2025-08-26 21:30:12,775 - src.modules.belief_module - INFO -   - 置信度: 0.3290
2025-08-26 21:30:12,779 - src.modules.belief_module - INFO - 信念 'belief_user_53_c2be104d' 的置信度 (0.3290) 低于求证阈值 (0.4500)
2025-08-26 21:30:12,779 - src.modules.belief_module - INFO - 正在为用户 'user_53' 触发对信念 'belief_user_53_c2be104d' 的求证机制...
2025-08-26 21:30:12,779 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 搜索结果:
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 1. 这个谣言虽然是假的，但它能火爆全网，恰恰说明了一个现实问题：我国对非婚同居关系的法律保护确实存在空白。现实中，一方为家庭付出多年，但因没有一纸婚书，分手或一方离世后权益无法保障的案例比比皆是。我们不应止于辟谣，更应思考，如何在保护私有财产权和维护传统婚姻制度之间，为“事实伴侣”提供一条人道的、有限的救济途径？比如在分割同居期间共同财产（析产）或请求扶养补偿方面，是否可以有更明确的规定？#法律思考 #非婚同居 #民法典
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 2. 大家别慌，我们国家的法律基石是很稳固的。关于财产，尤其是房子这种大事，核心就一条：不动产登记簿上写谁，就是谁的。这叫物权公示公信原则。别说同居一年，就是同居十年，只要房本没你名，法律上你就没份。所有让你焦虑的说法，都绕不开这个基本原则。所以，守好你的产权证，比什么都强。#物权法 #不动产登记 #硬核知识
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 生成了求证搜索行为: '理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境'
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-26 21:30:12,780 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境'
2025-08-26 21:30:12,781 - src.modules.belief_module - INFO - 认知冲击 (0.6907) 超过怀疑阈值 (0.3700)
2025-08-26 21:30:12,781 - src.modules.agent_module - INFO - 正在为用户 'user_53' 演化特质，认知冲击为: 0.6906927625791831, 内容情感强度: 0.4062499999999999
2025-08-26 21:30:12,783 - src.modules.agent_module - INFO - 基于内容情感强度 0.406 调整情绪波动性变化: -0.0104
2025-08-26 21:30:15,858 - src.modules.agent_module - INFO - 用户 'user_53' 的特质已更新:
2025-08-26 21:30:15,858 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.4391
2025-08-26 21:30:15,859 - src.modules.agent_module - INFO -   - 验证阈值: 0.4845
2025-08-26 21:30:15,859 - src.modules.agent_module - INFO -   - 情绪波动性: 0.3496
2025-08-26 21:30:15,859 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_53_c2be104d' 的变化，认知冲击为 0.6906927625791831...
2025-08-26 21:30:15,859 - src.modules.belief_module - INFO - 信念 'belief_user_53_c2be104d' 没有关联边，无需传播
2025-08-26 21:30:15,859 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.6907, 情感强度: 0.406
2025-08-26 21:30:15,859 - src.modules.belief_module - INFO - 将来自簇 cluster_81621c53 的 2 条记忆移入历史...
2025-08-26 21:30:27,132 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:30:27,134 - Engine - INFO - Cognitive processing for user 'user_53' finished.
2025-08-26 21:30:27,134 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-26 21:30:27,134 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-26 21:30:27,134 - Engine - INFO - Processing queued verification action for user 'user_53'
2025-08-26 21:30:27,134 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_53' -----
2025-08-26 21:30:35,585 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_53_637d8816).
2025-08-26 21:30:35,585 - Engine - INFO - No target_id provided for a content update action that requires one.
2025-08-26 21:30:35,586 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:30:35,586 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:30:35,586 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_53'...
2025-08-26 21:30:35,586 - src.modules.belief_module - INFO - 开始处理用户 'user_53' 的认知流程...
2025-08-26 21:30:35,601 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:30:35,601 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:30:35,601 - src.modules.belief_module - INFO - 用户 'user_53' 的短期记忆中未找到显著议题簇
2025-08-26 21:30:35,601 - Engine - INFO - Cognitive processing for user 'user_53' finished.
2025-08-26 21:30:35,602 - Engine - INFO - ----- Action 'READ_POST' for user 'user_53' processed successfully. -----
2025-08-26 21:30:35,602 - Engine - INFO - Queued verification action processed successfully
2025-08-26 21:30:35,602 - Engine - INFO - All verification actions processed.
2025-08-26 21:30:35,602 - Engine - INFO - Verification queue processing finished.
2025-08-26 21:30:35,602 - Engine - INFO - ----- Action 'CREATE_POST' for user 'user_53' processed successfully. -----
2025-08-26 21:30:35,603 - Simulator - INFO - Processing event from queue: READ_POST for user user_73
2025-08-26 21:30:35,603 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_73' -----
2025-08-26 21:30:42,907 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_73_642a8120).
2025-08-26 21:30:42,907 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:30:46,165 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:30:46,165 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:30:46,165 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:30:46,165 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_73'...
2025-08-26 21:30:46,165 - src.modules.belief_module - INFO - 开始处理用户 'user_73' 的认知流程...
2025-08-26 21:30:46,180 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:30:46,180 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:30:46,180 - src.modules.belief_module - INFO - 用户 'user_73' 的短期记忆中未找到显著议题簇
2025-08-26 21:30:46,181 - Engine - INFO - Cognitive processing for user 'user_73' finished.
2025-08-26 21:30:46,181 - Engine - INFO - ----- Action 'READ_POST' for user 'user_73' processed successfully. -----
2025-08-26 21:30:46,181 - Simulator - INFO - Processing event from queue: READ_POST for user user_49
2025-08-26 21:30:46,181 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_49' -----
2025-08-26 21:30:53,111 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_49_2053096d).
2025-08-26 21:30:53,111 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:30:55,889 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:30:55,890 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:30:55,890 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:30:55,890 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_49'...
2025-08-26 21:30:55,890 - src.modules.belief_module - INFO - 开始处理用户 'user_49' 的认知流程...
2025-08-26 21:30:55,905 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:30:55,905 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:30:55,905 - src.modules.belief_module - INFO - 用户 'user_49' 的短期记忆中未找到显著议题簇
2025-08-26 21:30:55,905 - Engine - INFO - Cognitive processing for user 'user_49' finished.
2025-08-26 21:30:55,905 - Engine - INFO - ----- Action 'READ_POST' for user 'user_49' processed successfully. -----
2025-08-26 21:30:55,906 - Simulator - INFO - Processing event from queue: READ_POST for user user_91
2025-08-26 21:30:55,906 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_91' -----
2025-08-26 21:31:03,020 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_91_a302c438).
2025-08-26 21:31:03,020 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:31:08,261 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:31:08,261 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:31:08,261 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:31:08,261 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_91'...
2025-08-26 21:31:08,261 - src.modules.belief_module - INFO - 开始处理用户 'user_91' 的认知流程...
【通知事件】为用户 user_26 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_26), 队列大小: 10
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_88
【通知事件】生成通知事件: 用户 user_88 接收 READ_POST 通知
【通知事件】为用户 user_88 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_88), 队列大小: 11
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_77
【通知事件】生成通知事件: 用户 user_77 接收 READ_POST 通知
【通知事件】为用户 user_77 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_77), 队列大小: 12
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_11
【通知事件】生成通知事件: 用户 user_11 接收 READ_POST 通知
【通知事件】为用户 user_11 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_11), 队列大小: 13
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_71
【通知事件】生成通知事件: 用户 user_71 接收 READ_POST 通知
【通知事件】为用户 user_71 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_71), 队列大小: 14
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_12
【通知事件】生成通知事件: 用户 user_12 接收 READ_POST 通知
【通知事件】为用户 user_12 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_12), 队列大小: 15
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_53 的认知处理...

【认知处理】开始处理用户 user_53 的认知流程
【阶段1+2】获取用户 user_53 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_53'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_53_c2be104d
【步骤 4/4】完成: 用户 user_53 的认知处理完成
【步骤 5/5】处理求证队列，队列大小: 1
【引擎】开始处理用户 user_53 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_53'...
MemoryModule: Successfully created memory 'mem_user_53_637d8816'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_53_637d8816)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_53 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_53 的认知处理...

【认知处理】开始处理用户 user_53 的认知流程
【阶段1+2】获取用户 user_53 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_53'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_53 的认知处理完成
【引擎】用户 user_53 的 READ_POST 操作处理成功
【步骤 5/5】完成: 求证队列处理完成
【引擎】用户 user_53 的 CREATE_POST 操作处理成功
【处理成功】事件处理成功: CREATE_POST (用户 user_53)
【处理事件】从队列中处理事件: READ_POST (用户 user_73)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_73 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_73'...
MemoryModule: Successfully created memory 'mem_user_73_642a8120'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_73_642a8120)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_73 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_73 的认知处理...

【认知处理】开始处理用户 user_73 的认知流程
【阶段1+2】获取用户 user_73 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_73'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_73 的认知处理完成
【引擎】用户 user_73 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_73)
【处理事件】从队列中处理事件: READ_POST (用户 user_49)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_49 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_49'...
MemoryModule: Successfully created memory 'mem_user_49_2053096d'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_49_2053096d)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_49 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_49 的认知处理...

【认知处理】开始处理用户 user_49 的认知流程
【阶段1+2】获取用户 user_49 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_49'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_49 的认知处理完成
【引擎】用户 user_49 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_49)
【处理事件】从队列中处理事件: READ_POST (用户 user_91)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_91 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_91'...
MemoryModule: Successfully created memory 'mem_user_91_a302c438'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_91_a302c438)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_91 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_91 的认知处理...

【认知处理】开始处理用户 user_91 的认知流程
【阶段1+2】获取用户 user_91 的短期记忆队列
2025-08-26 21:31:08,276 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:31:08,276 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:31:08,277 - src.modules.belief_module - INFO - 用户 'user_91' 的短期记忆中未找到显著议题簇
2025-08-26 21:31:08,277 - Engine - INFO - Cognitive processing for user 'user_91' finished.
2025-08-26 21:31:08,277 - Engine - INFO - ----- Action 'READ_POST' for user 'user_91' processed successfully. -----
2025-08-26 21:31:08,277 - Simulator - INFO - Processing event from queue: READ_POST for user user_54
2025-08-26 21:31:08,278 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_54' -----
2025-08-26 21:31:15,163 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_54_716bf402).
2025-08-26 21:31:15,163 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:31:17,883 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:31:17,884 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:31:17,884 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:31:17,884 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_54'...
2025-08-26 21:31:17,884 - src.modules.belief_module - INFO - 开始处理用户 'user_54' 的认知流程...
2025-08-26 21:31:17,898 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:31:17,899 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:31:17,899 - src.modules.belief_module - INFO - 用户 'user_54' 的短期记忆中未找到显著议题簇
2025-08-26 21:31:17,899 - Engine - INFO - Cognitive processing for user 'user_54' finished.
2025-08-26 21:31:17,899 - Engine - INFO - ----- Action 'READ_POST' for user 'user_54' processed successfully. -----
2025-08-26 21:31:17,900 - Simulator - INFO - Processing event from queue: READ_POST for user user_24
2025-08-26 21:31:17,900 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_24' -----
