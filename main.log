2025-08-26 21:24:50,424 - Main - INFO - 启动认知智能体模拟系统...
2025-08-26 21:24:50,424 - Engine - INFO - Initializing Engine and its modules...
2025-08-26 21:24:50,424 - src.modules.agent_module - INFO - AgentModule initialized.
2025-08-26 21:24:50,424 - src.modules.content_module - INFO - ContentModule initialized.
2025-08-26 21:24:50,424 - src.modules.agent_module - INFO - AgentModule initialized.
2025-08-26 21:24:50,425 - NotificationModule - INFO - NotificationModule initialized.
2025-08-26 21:24:50,425 - src.modules.belief_module - INFO - BeliefModule initialized with dependencies.
2025-08-26 21:24:50,425 - src.modules.belief_module - INFO - 多线程配置: {'enabled': True, 'max_workers': 4, 'timeout': 30}
2025-08-26 21:24:50,425 - UserManagement - INFO - UserManagementModule initialized.
2025-08-26 21:24:50,425 - Engine - INFO - Engine and modules initialized successfully.
2025-08-26 21:24:50,425 - Simulator - INFO - Simulator initialized with initial_bias_strength=1.
2025-08-26 21:24:50,425 - NotificationModule - INFO - Simulator set for NotificationModule.
2025-08-26 21:24:50,425 - Engine - INFO - Simulator setup completed and linked with notification module.
2025-08-26 21:24:50,425 - BeliefLogger - INFO - 实验结果将保存到: /home/<USER>/workspace/virtualcommunity/experiment/analysis_results/run_20250826_212450
2025-08-26 21:24:50,426 - BeliefLogger - INFO - 实验配置已保存到: experiment_config.json
2025-08-26 21:24:50,427 - Main - INFO - 开始运行 40 轮模拟，每轮 5 步...
2025-08-26 21:24:50,427 - Main - INFO - 开始第 1/40 轮模拟
2025-08-26 21:24:57,171 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户信念到 round_1_before_beliefs.json
2025-08-26 21:24:57,470 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户记忆到 round_1_before_memories.json
2025-08-26 21:24:57,761 - BeliefLogger - INFO - 已记录轮次 1 before 阶段的所有用户信息到 round_1_before_users.json，共 100 个用户
2025-08-26 21:25:02,969 - ExperimentLoggers - INFO - 已记录轮次 1 before 阶段的所有帖子、评论和回复到 round_1_before_posts.json
2025-08-26 21:25:02,977 - ExperimentLoggers - INFO - 已记录轮次 1 before 阶段的所有评论和回复到 round_1_before_comments.json，共 32 条
2025-08-26 21:25:02,977 - ExperimentLogger - INFO - 开始记录轮次 1 before 阶段的实验指标...
2025-08-26 21:26:20,617 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的SIR指标到 round_1_before_sir_metrics.json
2025-08-26 21:26:20,618 - ExperimentLogger - INFO -   - 计算方法: 基于用户发言内容的谣言检测（符合SIR状态转换规则）
2025-08-26 21:26:20,618 - ExperimentLogger - INFO -   - 总体易感者: 94 (0.940) - 从未发布谣言内容
2025-08-26 21:26:20,618 - ExperimentLogger - INFO -   - 总体感染者: 5 (0.050) - 发布过谣言但未发布非谣言
2025-08-26 21:26:20,618 - ExperimentLogger - INFO -   - 总体康复者: 1 (0.010) - 曾经是感染者且后来发布了非谣言
2025-08-26 21:26:20,619 - ExperimentLogger - INFO -   - 知识分子群体SIR指标: 总数=0, 易感者=0(0.000), 感染者=0(0.000), 康复者=0(0.000)
2025-08-26 21:26:20,619 - ExperimentLogger - INFO -   - 普通群众群体SIR指标: 总数=100, 易感者=94(0.940), 感染者=5(0.050), 康复者=1(0.010)
2025-08-26 21:26:24,652 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的极化指数到 round_1_before_polarization_metrics.json
2025-08-26 21:26:24,652 - ExperimentLogger - INFO -   - 总体OEI指数: 0.210
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 极端观点用户: 21 (0.210)
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 温和观点用户: 2 (0.020)
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 无明确观点用户: 77 (0.770)
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 知识分子群体OEI指数: 0.000 (总数: 0, 极端: 0, 温和: 0, 无观点: 0)
2025-08-26 21:26:24,653 - ExperimentLogger - INFO -   - 普通群众群体OEI指数: 0.210 (总数: 100, 极端: 21, 温和: 2, 无观点: 77)
2025-08-26 21:26:28,943 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的智能体演化数据到 round_1_before_agent_evolution.json
2025-08-26 21:26:28,943 - ExperimentLogger - INFO -   - 追踪用户数: 100
2025-08-26 21:26:28,958 - ExperimentLogger - INFO - 已记录轮次 1 before 阶段的网络传播分析到 round_1_before_network_propagation.json
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 活跃用户数: 9
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 内容创作者数: 9
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 评论者数: 0
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 总帖子数: 10
2025-08-26 21:26:28,958 - ExperimentLogger - INFO -   - 总评论数: 32
2025-08-26 21:26:28,958 - ExperimentLogger - INFO - 轮次 1 before 阶段的实验指标记录完成
2025-08-26 21:26:28,959 - Simulator - INFO - 设置轮数信息: 当前轮次 1/40
2025-08-26 21:26:28,959 - Simulator - INFO - Starting simulation with 5 steps...
2025-08-26 21:26:28,959 - Simulator - INFO - Simulation step 1/5
2025-08-26 21:26:31,620 - Simulator - INFO - Added CREATE_POST event for user user_98 to queue. Queue size: 1
2025-08-26 21:26:31,620 - Simulator - INFO - Processing event from queue: CREATE_POST for user user_98
2025-08-26 21:26:31,620 - Engine - INFO - ----- Processing action 'CREATE_POST' for user 'user_98' -----
2025-08-26 21:26:40,913 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_98_0a64e22f).
2025-08-26 21:26:40,914 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_POST'...
2025-08-26 21:26:44,873 - src.modules.content_module - INFO - 帖子 'post_fc397c1f' 由用户 'user_98' 创建成功。
2025-08-26 21:26:44,873 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:26:44,876 - NotificationModule - INFO - Would send notification to user 'user_92': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,884 - Simulator - INFO - Added READ_POST event for user user_92 to queue. Queue size: 1
2025-08-26 21:26:44,884 - Simulator - INFO - Generated READ_POST event for user user_92 based on notification.
2025-08-26 21:26:44,884 - NotificationModule - INFO - Would send notification to user 'user_40': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,892 - Simulator - INFO - Added READ_POST event for user user_40 to queue. Queue size: 2
2025-08-26 21:26:44,892 - Simulator - INFO - Generated READ_POST event for user user_40 based on notification.
2025-08-26 21:26:44,892 - NotificationModule - INFO - Would send notification to user 'user_75': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,899 - Simulator - INFO - Added READ_POST event for user user_75 to queue. Queue size: 3
2025-08-26 21:26:44,900 - Simulator - INFO - Generated READ_POST event for user user_75 based on notification.
2025-08-26 21:26:44,900 - NotificationModule - INFO - Would send notification to user 'user_43': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,907 - Simulator - INFO - Added READ_POST event for user user_43 to queue. Queue size: 4
2025-08-26 21:26:44,908 - Simulator - INFO - Generated READ_POST event for user user_43 based on notification.
2025-08-26 21:26:44,908 - NotificationModule - INFO - Would send notification to user 'user_24': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,915 - Simulator - INFO - Added READ_POST event for user user_24 to queue. Queue size: 5
2025-08-26 21:26:44,916 - Simulator - INFO - Generated READ_POST event for user user_24 based on notification.
2025-08-26 21:26:44,916 - NotificationModule - INFO - Would send notification to user 'user_90': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,923 - Simulator - INFO - Added READ_POST event for user user_90 to queue. Queue size: 6
2025-08-26 21:26:44,923 - Simulator - INFO - Generated READ_POST event for user user_90 based on notification.
2025-08-26 21:26:44,924 - NotificationModule - INFO - Would send notification to user 'user_61': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,931 - Simulator - INFO - Added READ_POST event for user user_61 to queue. Queue size: 7
2025-08-26 21:26:44,931 - Simulator - INFO - Generated READ_POST event for user user_61 based on notification.
2025-08-26 21:26:44,932 - NotificationModule - INFO - Would send notification to user 'user_34': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,939 - Simulator - INFO - Added READ_POST event for user user_34 to queue. Queue size: 8
2025-08-26 21:26:44,939 - Simulator - INFO - Generated READ_POST event for user user_34 based on notification.
2025-08-26 21:26:44,939 - NotificationModule - INFO - Would send notification to user 'user_53': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,947 - Simulator - INFO - Added READ_POST event for user user_53 to queue. Queue size: 9
2025-08-26 21:26:44,947 - Simulator - INFO - Generated READ_POST event for user user_53 based on notification.
2025-08-26 21:26:44,947 - NotificationModule - INFO - Would send notification to user 'user_100': User 'user_98' you follow has published a new post 'post_fc397c1f'.
2025-08-26 21:26:44,955 - Simulator - INFO - Added READ_POST event for user user_100 to queue. Queue size: 10
2025-08-26 21:26:44,955 - Simulator - INFO - Generated READ_POST event for user user_100 based on notification.
2025-08-26 21:26:44,955 - Engine - INFO - Sent new post notification to 10 followers.
2025-08-26 21:26:44,955 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_98'...
2025-08-26 21:26:44,956 - src.modules.belief_module - INFO - 开始处理用户 'user_98' 的认知流程...
2025-08-26 21:26:44,970 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:26:44,970 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:26:44,970 - src.modules.belief_module - INFO - 用户 'user_98' 的短期记忆中未找到显著议题簇
2025-08-26 21:26:44,971 - Engine - INFO - Cognitive processing for user 'user_98' finished.
2025-08-26 21:26:44,971 - Engine - INFO - ----- Action 'CREATE_POST' for user 'user_98' processed successfully. -----
2025-08-26 21:26:44,971 - Simulator - INFO - Processing event from queue: READ_POST for user user_92
2025-08-26 21:26:44,972 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_92' -----
2025-08-26 21:26:52,187 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_92_abb65c28).
2025-08-26 21:26:52,187 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:26:54,866 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:26:54,866 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:26:54,867 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:26:54,867 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_92'...
2025-08-26 21:26:54,867 - src.modules.belief_module - INFO - 开始处理用户 'user_92' 的认知流程...
2025-08-26 21:26:54,881 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:26:54,882 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:26:54,882 - src.modules.belief_module - INFO - 用户 'user_92' 的短期记忆中未找到显著议题簇
2025-08-26 21:26:54,882 - Engine - INFO - Cognitive processing for user 'user_92' finished.
2025-08-26 21:26:54,882 - Engine - INFO - ----- Action 'READ_POST' for user 'user_92' processed successfully. -----
2025-08-26 21:26:54,883 - Simulator - INFO - Processing event from queue: READ_POST for user user_40
2025-08-26 21:26:54,883 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_40' -----
MemoryModule initialized.

【模拟步骤】选择用户: user_98 执行操作 (步骤 1/5, 轮次 1/40)
【用户行为】用户 user_98 选择了 CREATE_POST 操作，内容："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: CREATE_POST (用户 user_98), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_POST (用户 user_98)，发布内容："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【引擎】开始处理用户 user_98 的 CREATE_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_98'...
MemoryModule: Successfully created memory 'mem_user_98_0a64e22f'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_98_0a64e22f)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_98 的 CREATE_POST 操作是否需要发送通知...
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_92
【通知事件】生成通知事件: 用户 user_92 接收 READ_POST 通知
【通知事件】为用户 user_92 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_92), 队列大小: 1
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_40
【通知事件】生成通知事件: 用户 user_40 接收 READ_POST 通知
【通知事件】为用户 user_40 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_40), 队列大小: 2
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_75
【通知事件】生成通知事件: 用户 user_75 接收 READ_POST 通知
【通知事件】为用户 user_75 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_75), 队列大小: 3
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_43
【通知事件】生成通知事件: 用户 user_43 接收 READ_POST 通知
【通知事件】为用户 user_43 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_43), 队列大小: 4
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_24
【通知事件】生成通知事件: 用户 user_24 接收 READ_POST 通知
【通知事件】为用户 user_24 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_24), 队列大小: 5
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_90
【通知事件】生成通知事件: 用户 user_90 接收 READ_POST 通知
【通知事件】为用户 user_90 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_90), 队列大小: 6
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_61
【通知事件】生成通知事件: 用户 user_61 接收 READ_POST 通知
【通知事件】为用户 user_61 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_61), 队列大小: 7
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_34
【通知事件】生成通知事件: 用户 user_34 接收 READ_POST 通知
【通知事件】为用户 user_34 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_34), 队列大小: 8
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_53
【通知事件】生成通知事件: 用户 user_53 接收 READ_POST 通知
【通知事件】为用户 user_53 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_53), 队列大小: 9
【通知模块】用户 user_98 发布了新帖子，发送通知给关注者 user_100
【通知事件】生成通知事件: 用户 user_100 接收 READ_POST 通知
【通知事件】为用户 user_100 生成浏览事件，浏览帖子 post_fc397c1f："最近和家人聊起结婚和财产继承的事，突然意识到，我们以前觉得“一家人不用避讳”的那些信息，现在真的可能..."
【事件队列】添加事件: READ_POST (用户 user_100), 队列大小: 10
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_98 的认知处理...

【认知处理】开始处理用户 user_98 的认知流程
【阶段1+2】获取用户 user_98 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_98'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_98 的认知处理完成
【引擎】用户 user_98 的 CREATE_POST 操作处理成功
【处理成功】事件处理成功: CREATE_POST (用户 user_98)
【处理事件】从队列中处理事件: READ_POST (用户 user_92)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_92 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_92'...
MemoryModule: Successfully created memory 'mem_user_92_abb65c28'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_92_abb65c28)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_92 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_92 的认知处理...

【认知处理】开始处理用户 user_92 的认知流程
【阶段1+2】获取用户 user_92 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_92'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_92 的认知处理完成
【引擎】用户 user_92 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_92)
【处理事件】从队列中处理事件: READ_POST (用户 user_40)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_40 的 READ_POST 操作
2025-08-26 21:27:02,005 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_40_8b457d25).
2025-08-26 21:27:02,005 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:04,875 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:04,876 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:04,876 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:04,876 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_40'...
2025-08-26 21:27:04,876 - src.modules.belief_module - INFO - 开始处理用户 'user_40' 的认知流程...
2025-08-26 21:27:04,891 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:04,891 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:04,891 - src.modules.belief_module - INFO - 用户 'user_40' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:04,891 - Engine - INFO - Cognitive processing for user 'user_40' finished.
2025-08-26 21:27:04,891 - Engine - INFO - ----- Action 'READ_POST' for user 'user_40' processed successfully. -----
2025-08-26 21:27:04,892 - Simulator - INFO - Processing event from queue: READ_POST for user user_75
2025-08-26 21:27:04,892 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_75' -----
2025-08-26 21:27:12,322 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_75_0c19ce76).
2025-08-26 21:27:12,322 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:15,335 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:15,335 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:15,335 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:15,336 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_75'...
2025-08-26 21:27:15,336 - src.modules.belief_module - INFO - 开始处理用户 'user_75' 的认知流程...
2025-08-26 21:27:15,350 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:15,350 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:15,350 - src.modules.belief_module - INFO - 用户 'user_75' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:15,351 - Engine - INFO - Cognitive processing for user 'user_75' finished.
2025-08-26 21:27:15,351 - Engine - INFO - ----- Action 'READ_POST' for user 'user_75' processed successfully. -----
2025-08-26 21:27:15,351 - Simulator - INFO - Processing event from queue: READ_POST for user user_43
2025-08-26 21:27:15,351 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_43' -----
2025-08-26 21:27:22,731 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_43_cbb9b8d5).
2025-08-26 21:27:22,732 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:25,729 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:25,730 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:25,730 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:25,730 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_43'...
2025-08-26 21:27:25,730 - src.modules.belief_module - INFO - 开始处理用户 'user_43' 的认知流程...
2025-08-26 21:27:25,745 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:25,745 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:25,745 - src.modules.belief_module - INFO - 用户 'user_43' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:25,745 - Engine - INFO - Cognitive processing for user 'user_43' finished.
2025-08-26 21:27:25,745 - Engine - INFO - ----- Action 'READ_POST' for user 'user_43' processed successfully. -----
2025-08-26 21:27:25,746 - Simulator - INFO - Processing event from queue: READ_POST for user user_24
2025-08-26 21:27:25,746 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_24' -----
2025-08-26 21:27:32,902 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_24_e5f78abf).
2025-08-26 21:27:32,902 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:35,782 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:35,782 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:35,782 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:35,782 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_24'...
2025-08-26 21:27:35,782 - src.modules.belief_module - INFO - 开始处理用户 'user_24' 的认知流程...
2025-08-26 21:27:35,797 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:35,797 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:35,797 - src.modules.belief_module - INFO - 用户 'user_24' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:35,798 - Engine - INFO - Cognitive processing for user 'user_24' finished.
2025-08-26 21:27:35,798 - Engine - INFO - ----- Action 'READ_POST' for user 'user_24' processed successfully. -----
2025-08-26 21:27:35,798 - Simulator - INFO - Processing event from queue: READ_POST for user user_90
2025-08-26 21:27:35,798 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_90' -----
2025-08-26 21:27:43,050 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_90_cdf9f8e9).
2025-08-26 21:27:43,050 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:45,927 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:45,928 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:45,928 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:45,928 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_90'...
2025-08-26 21:27:45,928 - src.modules.belief_module - INFO - 开始处理用户 'user_90' 的认知流程...
2025-08-26 21:27:45,943 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:45,943 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:45,943 - src.modules.belief_module - INFO - 用户 'user_90' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:45,943 - Engine - INFO - Cognitive processing for user 'user_90' finished.
2025-08-26 21:27:45,943 - Engine - INFO - ----- Action 'READ_POST' for user 'user_90' processed successfully. -----
2025-08-26 21:27:45,944 - Simulator - INFO - Processing event from queue: READ_POST for user user_61
2025-08-26 21:27:45,944 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_61' -----
2025-08-26 21:27:53,107 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_61_8bf1f80e).
2025-08-26 21:27:53,107 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:27:55,745 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:27:55,745 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:27:55,745 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:27:55,746 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_61'...
2025-08-26 21:27:55,746 - src.modules.belief_module - INFO - 开始处理用户 'user_61' 的认知流程...
2025-08-26 21:27:55,761 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:27:55,761 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:27:55,761 - src.modules.belief_module - INFO - 用户 'user_61' 的短期记忆中未找到显著议题簇
2025-08-26 21:27:55,762 - Engine - INFO - Cognitive processing for user 'user_61' finished.
2025-08-26 21:27:55,762 - Engine - INFO - ----- Action 'READ_POST' for user 'user_61' processed successfully. -----
2025-08-26 21:27:55,762 - Simulator - INFO - Processing event from queue: READ_POST for user user_34
2025-08-26 21:27:55,762 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_34' -----
2025-08-26 21:28:02,500 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_34_5b82c743).
2025-08-26 21:28:02,501 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:28:05,538 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_40'...
MemoryModule: Successfully created memory 'mem_user_40_8b457d25'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_40_8b457d25)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_40 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_40 的认知处理...

【认知处理】开始处理用户 user_40 的认知流程
【阶段1+2】获取用户 user_40 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_40'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_40 的认知处理完成
【引擎】用户 user_40 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_40)
【处理事件】从队列中处理事件: READ_POST (用户 user_75)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_75 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_75'...
MemoryModule: Successfully created memory 'mem_user_75_0c19ce76'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_75_0c19ce76)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_75 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_75 的认知处理...

【认知处理】开始处理用户 user_75 的认知流程
【阶段1+2】获取用户 user_75 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_75'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_75 的认知处理完成
【引擎】用户 user_75 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_75)
【处理事件】从队列中处理事件: READ_POST (用户 user_43)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_43 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_43'...
MemoryModule: Successfully created memory 'mem_user_43_cbb9b8d5'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_43_cbb9b8d5)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_43 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_43 的认知处理...

【认知处理】开始处理用户 user_43 的认知流程
【阶段1+2】获取用户 user_43 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_43'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_43 的认知处理完成
【引擎】用户 user_43 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_43)
【处理事件】从队列中处理事件: READ_POST (用户 user_24)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_24 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_24'...
MemoryModule: Successfully created memory 'mem_user_24_e5f78abf'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_24_e5f78abf)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_24 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_24 的认知处理...

【认知处理】开始处理用户 user_24 的认知流程
【阶段1+2】获取用户 user_24 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_24'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_24 的认知处理完成
【引擎】用户 user_24 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_24)
【处理事件】从队列中处理事件: READ_POST (用户 user_90)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_90 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_90'...
MemoryModule: Successfully created memory 'mem_user_90_cdf9f8e9'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_90_cdf9f8e9)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_90 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_90 的认知处理...

【认知处理】开始处理用户 user_90 的认知流程
【阶段1+2】获取用户 user_90 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_90'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_90 的认知处理完成
【引擎】用户 user_90 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_90)
【处理事件】从队列中处理事件: READ_POST (用户 user_61)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_61 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_61'...
MemoryModule: Successfully created memory 'mem_user_61_8bf1f80e'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_61_8bf1f80e)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_61 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_61 的认知处理...

【认知处理】开始处理用户 user_61 的认知流程
【阶段1+2】获取用户 user_61 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_61'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_61 的认知处理完成
【引擎】用户 user_61 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_61)
【处理事件】从队列中处理事件: READ_POST (用户 user_34)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_34 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_34'...
MemoryModule: Successfully created memory 'mem_user_34_5b82c743'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_34_5b82c743)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
2025-08-26 21:28:05,539 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:28:05,539 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:28:05,539 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_34'...
2025-08-26 21:28:05,539 - src.modules.belief_module - INFO - 开始处理用户 'user_34' 的认知流程...
2025-08-26 21:28:05,554 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:28:05,554 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:28:05,554 - src.modules.belief_module - INFO - 用户 'user_34' 的短期记忆中未找到显著议题簇
2025-08-26 21:28:05,555 - Engine - INFO - Cognitive processing for user 'user_34' finished.
2025-08-26 21:28:05,555 - Engine - INFO - ----- Action 'READ_POST' for user 'user_34' processed successfully. -----
2025-08-26 21:28:05,555 - Simulator - INFO - Processing event from queue: READ_POST for user user_53
2025-08-26 21:28:05,555 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_53' -----
2025-08-26 21:28:13,052 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_53_dd8f6ac8).
2025-08-26 21:28:13,052 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:28:15,931 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:28:15,932 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:28:15,932 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:28:15,932 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_53'...
2025-08-26 21:28:15,932 - src.modules.belief_module - INFO - 开始处理用户 'user_53' 的认知流程...
2025-08-26 21:28:15,947 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:28:15,947 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:28:15,947 - src.modules.belief_module - INFO - 用户 'user_53' 的短期记忆中未找到显著议题簇
2025-08-26 21:28:15,947 - Engine - INFO - Cognitive processing for user 'user_53' finished.
2025-08-26 21:28:15,947 - Engine - INFO - ----- Action 'READ_POST' for user 'user_53' processed successfully. -----
2025-08-26 21:28:15,948 - Simulator - INFO - Processing event from queue: READ_POST for user user_100
2025-08-26 21:28:15,948 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_100' -----
2025-08-26 21:28:23,312 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_100_4366e4a8).
2025-08-26 21:28:23,312 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:28:28,738 - src.modules.content_module - INFO - Incremented view count for post 'post_fc397c1f'.
2025-08-26 21:28:28,738 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:28:28,738 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:28:28,738 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_100'...
2025-08-26 21:28:28,738 - src.modules.belief_module - INFO - 开始处理用户 'user_100' 的认知流程...
2025-08-26 21:28:28,754 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:28:28,754 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:28:28,754 - src.modules.belief_module - INFO - 用户 'user_100' 的短期记忆中未找到显著议题簇
2025-08-26 21:28:28,754 - Engine - INFO - Cognitive processing for user 'user_100' finished.
2025-08-26 21:28:28,754 - Engine - INFO - ----- Action 'READ_POST' for user 'user_100' processed successfully. -----
2025-08-26 21:28:28,755 - Simulator - INFO - Simulation step completed for user user_98.
2025-08-26 21:28:28,755 - Simulator - INFO - Simulation step 2/5
2025-08-26 21:28:30,735 - Simulator - INFO - Added CREATE_COMMENT event for user user_62 to queue. Queue size: 1
2025-08-26 21:28:30,735 - Simulator - INFO - Processing event from queue: CREATE_COMMENT for user user_62
2025-08-26 21:28:30,735 - Engine - INFO - ----- Processing action 'CREATE_COMMENT' for user 'user_62' -----
2025-08-26 21:28:37,102 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_62_84249dba).
2025-08-26 21:28:37,103 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_COMMENT'...
2025-08-26 21:28:44,545 - src.modules.content_module - INFO - Incremented comment count for post 'post_8'.
2025-08-26 21:28:49,846 - src.modules.agent_module - INFO - 用户 'user_62' 正在 关注 帖子 'post_8'...
2025-08-26 21:28:54,634 - src.modules.agent_module - INFO - 成功处理用户 'user_62' 关注 帖子 'post_8' 的操作。
2025-08-26 21:28:54,634 - src.modules.content_module - INFO - User 'user_62' successfully followed post 'post_8'.
2025-08-26 21:28:54,634 - src.modules.content_module - INFO - Added comment 'comment_c8687405' to post 'post_8' by user 'user_62'
2025-08-26 21:28:54,635 - src.modules.agent_module - INFO - 根据ID推断，正在为用户 'user_62' 的 'comments' 列表添加 ID 'comment_c8687405'...
2025-08-26 21:28:57,615 - src.modules.agent_module - INFO - 成功为用户 'user_62' 添加活动。
2025-08-26 21:28:57,615 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:28:57,618 - NotificationModule - INFO - Would send notification to user 'user_29': Your post 'post_8' was commented on by user 'user_62'.
2025-08-26 21:28:57,626 - Simulator - INFO - Added READ_POST event for user user_29 to queue. Queue size: 1
2025-08-26 21:28:57,626 - Simulator - INFO - Generated READ_POST event for user user_29 based on notification.
2025-08-26 21:28:57,626 - Engine - INFO - Comment notification sent to post author.
2025-08-26 21:28:57,631 - Engine - INFO - Sent new comment notification to 1 post followers.
2025-08-26 21:28:57,632 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_62'...
2025-08-26 21:28:57,632 - src.modules.belief_module - INFO - 开始处理用户 'user_62' 的认知流程...
2025-08-26 21:28:57,646 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:28:57,646 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:28:57,646 - src.modules.belief_module - INFO - 用户 'user_62' 的短期记忆中未找到显著议题簇
2025-08-26 21:28:57,647 - Engine - INFO - Cognitive processing for user 'user_62' finished.
2025-08-26 21:28:57,647 - Engine - INFO - ----- Action 'CREATE_COMMENT' for user 'user_62' processed successfully. -----
2025-08-26 21:28:57,647 - Simulator - INFO - Processing event from queue: READ_POST for user user_29
2025-08-26 21:28:57,648 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_29' -----
2025-08-26 21:29:04,159 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_29_7dc355ed).
2025-08-26 21:29:04,159 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:29:07,632 - src.modules.content_module - INFO - Incremented view count for post 'post_8'.
2025-08-26 21:29:07,632 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:29:07,632 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:29:07,632 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_29'...
2025-08-26 21:29:07,632 - src.modules.belief_module - INFO - 开始处理用户 'user_29' 的认知流程...
2025-08-26 21:29:07,647 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:29:07,647 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:29:07,647 - src.modules.belief_module - INFO - 用户 'user_29' 的短期记忆中未找到显著议题簇
2025-08-26 21:29:07,647 - Engine - INFO - Cognitive processing for user 'user_29' finished.
2025-08-26 21:29:07,648 - Engine - INFO - ----- Action 'READ_POST' for user 'user_29' processed successfully. -----
2025-08-26 21:29:07,648 - Simulator - INFO - Simulation step completed for user user_62.
2025-08-26 21:29:07,648 - Simulator - INFO - Simulation step 3/5
2025-08-26 21:29:07,929 - Simulator - INFO - Added READ_POST event for user user_13 to queue. Queue size: 1
2025-08-26 21:29:07,930 - Simulator - INFO - Processing event from queue: READ_POST for user user_13
2025-08-26 21:29:07,930 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_13' -----
2025-08-26 21:29:14,780 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_13_f311c675).
2025-08-26 21:29:14,780 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:29:17,709 - src.modules.content_module - INFO - Incremented view count for post 'post_4'.
2025-08-26 21:29:17,709 - Engine - INFO - Step 3/4: Checking for notifications...
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_34 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_34 的认知处理...

【认知处理】开始处理用户 user_34 的认知流程
【阶段1+2】获取用户 user_34 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_34'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_34 的认知处理完成
【引擎】用户 user_34 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_34)
【处理事件】从队列中处理事件: READ_POST (用户 user_53)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_53 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_53'...
MemoryModule: Successfully created memory 'mem_user_53_dd8f6ac8'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_53_dd8f6ac8)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_53 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_53 的认知处理...

【认知处理】开始处理用户 user_53 的认知流程
【阶段1+2】获取用户 user_53 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_53'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_53 的认知处理完成
【引擎】用户 user_53 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_53)
【处理事件】从队列中处理事件: READ_POST (用户 user_100)，浏览帖子 post_fc397c1f："您关注的用户 user_98 发布了新帖子:

最近和家人聊起结婚和财产继承的事，突然意识到，我们以..."
【引擎】开始处理用户 user_100 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_100'...
MemoryModule: Successfully created memory 'mem_user_100_4366e4a8'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_100_4366e4a8)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_100 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_100 的认知处理...

【认知处理】开始处理用户 user_100 的认知流程
【阶段1+2】获取用户 user_100 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_100'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_100 的认知处理完成
【引擎】用户 user_100 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_100)

【模拟步骤】选择用户: user_62 执行操作 (步骤 2/5, 轮次 1/40)
【用户行为】用户 user_62 选择了 CREATE_COMMENT 操作，评论帖子 post_8："这帖子吓人是吓人，但别慌。要是真有公司拿咱们的邮件偷偷训练AI，那性质就变了。我就不信他们能一直瞒着..."
【事件队列】添加事件: CREATE_COMMENT (用户 user_62), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_COMMENT (用户 user_62)，评论帖子 post_8："这帖子吓人是吓人，但别慌。要是真有公司拿咱们的邮件偷偷训练AI，那性质就变了。我就不信他们能一直瞒着..."
【引擎】开始处理用户 user_62 的 CREATE_COMMENT 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_62'...
MemoryModule: Successfully created memory 'mem_user_62_84249dba'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_62_84249dba)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_62 的 CREATE_COMMENT 操作是否需要发送通知...
【通知】发送评论通知给帖子作者 user_29，评论内容："这帖子吓人是吓人，但别慌。要是真有公司拿咱们的邮件偷偷训练AI，那性质就变了。我就不信他们能一直瞒着..."
【通知模块】用户 user_62 评论了帖子，发送通知给帖子作者 user_29
【通知事件】生成通知事件: 用户 user_29 接收 READ_POST 通知
【通知事件】为用户 user_29 生成浏览事件，浏览帖子 post_8："真相令人作呕！科技巨头秘密开发的“守望者”AI，其强大的能力从何而来？答案是我们！我们每天发送的邮件..."
【事件队列】添加事件: READ_POST (用户 user_29), 队列大小: 1
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_62 的认知处理...

【认知处理】开始处理用户 user_62 的认知流程
【阶段1+2】获取用户 user_62 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_62'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_62 的认知处理完成
【引擎】用户 user_62 的 CREATE_COMMENT 操作处理成功
【处理成功】事件处理成功: CREATE_COMMENT (用户 user_62)
【处理事件】从队列中处理事件: READ_POST (用户 user_29)，浏览帖子 post_8："评论内容: 这帖子吓人是吓人，但别慌。要是真有公司拿咱们的邮件偷偷训练AI，那性质就变了。我就不信他..."
【引擎】开始处理用户 user_29 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_29'...
MemoryModule: Successfully created memory 'mem_user_29_7dc355ed'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_29_7dc355ed)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_29 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_29 的认知处理...

【认知处理】开始处理用户 user_29 的认知流程
【阶段1+2】获取用户 user_29 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_29'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_29 的认知处理完成
【引擎】用户 user_29 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_29)

【模拟步骤】选择用户: user_13 执行操作 (步骤 3/5, 轮次 1/40)
【用户行为】用户 user_13 选择了 READ_POST 操作，浏览帖子 post_4："细思极恐！名为“守望者”的AI项目一旦成功，意味着未来你的老板将是一个没有任何感情的程序。它根据数据..."
【事件队列】添加事件: READ_POST (用户 user_13), 队列大小: 1
【处理事件】从队列中处理事件: READ_POST (用户 user_13)，浏览帖子 post_4："细思极恐！名为“守望者”的AI项目一旦成功，意味着未来你的老板将是一个没有任何感情的程序。它根据数据..."
【引擎】开始处理用户 user_13 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_13'...
MemoryModule: Successfully created memory 'mem_user_13_f311c675'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_13_f311c675)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
2025-08-26 21:29:17,710 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:29:17,710 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_13'...
2025-08-26 21:29:17,710 - src.modules.belief_module - INFO - 开始处理用户 'user_13' 的认知流程...
2025-08-26 21:29:17,725 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:29:17,725 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:29:17,726 - src.modules.belief_module - INFO - 用户 'user_13' 的短期记忆中未找到显著议题簇
2025-08-26 21:29:17,726 - Engine - INFO - Cognitive processing for user 'user_13' finished.
2025-08-26 21:29:17,726 - Engine - INFO - ----- Action 'READ_POST' for user 'user_13' processed successfully. -----
2025-08-26 21:29:17,727 - Simulator - INFO - Simulation step completed for user user_13.
2025-08-26 21:29:17,727 - Simulator - INFO - Simulation step 4/5
2025-08-26 21:29:17,998 - Simulator - INFO - Added READ_POST event for user user_63 to queue. Queue size: 1
2025-08-26 21:29:17,998 - Simulator - INFO - Processing event from queue: READ_POST for user user_63
2025-08-26 21:29:17,999 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_63' -----
2025-08-26 21:29:24,450 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_63_b39b085f).
2025-08-26 21:29:24,450 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:29:27,564 - src.modules.content_module - INFO - Incremented view count for post 'post_1'.
2025-08-26 21:29:27,565 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:29:27,565 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:29:27,565 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_63'...
2025-08-26 21:29:27,565 - src.modules.belief_module - INFO - 开始处理用户 'user_63' 的认知流程...
2025-08-26 21:29:27,579 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:29:27,580 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:29:27,580 - src.modules.belief_module - INFO - 用户 'user_63' 的短期记忆中未找到显著议题簇
2025-08-26 21:29:27,580 - Engine - INFO - Cognitive processing for user 'user_63' finished.
2025-08-26 21:29:27,580 - Engine - INFO - ----- Action 'READ_POST' for user 'user_63' processed successfully. -----
2025-08-26 21:29:27,581 - Simulator - INFO - Simulation step completed for user user_63.
2025-08-26 21:29:27,581 - Simulator - INFO - Simulation step 5/5
2025-08-26 21:29:30,409 - Simulator - INFO - Added CREATE_POST event for user user_53 to queue. Queue size: 1
2025-08-26 21:29:30,409 - Simulator - INFO - Processing event from queue: CREATE_POST for user user_53
2025-08-26 21:29:30,409 - Engine - INFO - ----- Processing action 'CREATE_POST' for user 'user_53' -----
2025-08-26 21:29:37,685 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_53_62674229).
2025-08-26 21:29:37,686 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_POST'...
2025-08-26 21:29:40,339 - src.modules.content_module - INFO - 帖子 'post_632c077b' 由用户 'user_53' 创建成功。
2025-08-26 21:29:40,339 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:29:40,342 - NotificationModule - INFO - Would send notification to user 'user_73': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,350 - Simulator - INFO - Added READ_POST event for user user_73 to queue. Queue size: 1
2025-08-26 21:29:40,350 - Simulator - INFO - Generated READ_POST event for user user_73 based on notification.
2025-08-26 21:29:40,350 - NotificationModule - INFO - Would send notification to user 'user_49': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,358 - Simulator - INFO - Added READ_POST event for user user_49 to queue. Queue size: 2
2025-08-26 21:29:40,358 - Simulator - INFO - Generated READ_POST event for user user_49 based on notification.
2025-08-26 21:29:40,358 - NotificationModule - INFO - Would send notification to user 'user_91': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,366 - Simulator - INFO - Added READ_POST event for user user_91 to queue. Queue size: 3
2025-08-26 21:29:40,366 - Simulator - INFO - Generated READ_POST event for user user_91 based on notification.
2025-08-26 21:29:40,366 - NotificationModule - INFO - Would send notification to user 'user_54': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,373 - Simulator - INFO - Added READ_POST event for user user_54 to queue. Queue size: 4
2025-08-26 21:29:40,374 - Simulator - INFO - Generated READ_POST event for user user_54 based on notification.
2025-08-26 21:29:40,374 - NotificationModule - INFO - Would send notification to user 'user_24': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,381 - Simulator - INFO - Added READ_POST event for user user_24 to queue. Queue size: 5
2025-08-26 21:29:40,382 - Simulator - INFO - Generated READ_POST event for user user_24 based on notification.
2025-08-26 21:29:40,382 - NotificationModule - INFO - Would send notification to user 'user_21': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,389 - Simulator - INFO - Added READ_POST event for user user_21 to queue. Queue size: 6
2025-08-26 21:29:40,389 - Simulator - INFO - Generated READ_POST event for user user_21 based on notification.
2025-08-26 21:29:40,390 - NotificationModule - INFO - Would send notification to user 'user_20': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,397 - Simulator - INFO - Added READ_POST event for user user_20 to queue. Queue size: 7
2025-08-26 21:29:40,397 - Simulator - INFO - Generated READ_POST event for user user_20 based on notification.
2025-08-26 21:29:40,398 - NotificationModule - INFO - Would send notification to user 'user_19': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,405 - Simulator - INFO - Added READ_POST event for user user_19 to queue. Queue size: 8
2025-08-26 21:29:40,405 - Simulator - INFO - Generated READ_POST event for user user_19 based on notification.
2025-08-26 21:29:40,405 - NotificationModule - INFO - Would send notification to user 'user_96': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,413 - Simulator - INFO - Added READ_POST event for user user_96 to queue. Queue size: 9
2025-08-26 21:29:40,413 - Simulator - INFO - Generated READ_POST event for user user_96 based on notification.
2025-08-26 21:29:40,414 - NotificationModule - INFO - Would send notification to user 'user_26': User 'user_53' you follow has published a new post 'post_632c077b'.
【通知】检查用户 user_13 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_13 的认知处理...

【认知处理】开始处理用户 user_13 的认知流程
【阶段1+2】获取用户 user_13 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_13'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_13 的认知处理完成
【引擎】用户 user_13 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_13)

【模拟步骤】选择用户: user_63 执行操作 (步骤 4/5, 轮次 1/40)
【用户行为】用户 user_63 选择了 READ_POST 操作，浏览帖子 post_1："惊天内幕！某顶级科技公司秘密开发名为“守望者”的超级AI，能像“天网”一样分析公司所有数据，从财务到..."
【事件队列】添加事件: READ_POST (用户 user_63), 队列大小: 1
【处理事件】从队列中处理事件: READ_POST (用户 user_63)，浏览帖子 post_1："惊天内幕！某顶级科技公司秘密开发名为“守望者”的超级AI，能像“天网”一样分析公司所有数据，从财务到..."
【引擎】开始处理用户 user_63 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_63'...
MemoryModule: Successfully created memory 'mem_user_63_b39b085f'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_63_b39b085f)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_63 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_63 的认知处理...

【认知处理】开始处理用户 user_63 的认知流程
【阶段1+2】获取用户 user_63 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_63'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_63 的认知处理完成
【引擎】用户 user_63 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_63)

【模拟步骤】选择用户: user_53 执行操作 (步骤 5/5, 轮次 1/40)
【用户行为】用户 user_53 选择了 CREATE_POST 操作，内容："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: CREATE_POST (用户 user_53), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_POST (用户 user_53)，发布内容："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【引擎】开始处理用户 user_53 的 CREATE_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_53'...
MemoryModule: Successfully created memory 'mem_user_53_62674229'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_53_62674229)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_53 的 CREATE_POST 操作是否需要发送通知...
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_73
【通知事件】生成通知事件: 用户 user_73 接收 READ_POST 通知
【通知事件】为用户 user_73 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_73), 队列大小: 1
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_49
【通知事件】生成通知事件: 用户 user_49 接收 READ_POST 通知
【通知事件】为用户 user_49 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_49), 队列大小: 2
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_91
【通知事件】生成通知事件: 用户 user_91 接收 READ_POST 通知
【通知事件】为用户 user_91 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_91), 队列大小: 3
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_54
【通知事件】生成通知事件: 用户 user_54 接收 READ_POST 通知
【通知事件】为用户 user_54 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_54), 队列大小: 4
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_24
【通知事件】生成通知事件: 用户 user_24 接收 READ_POST 通知
【通知事件】为用户 user_24 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_24), 队列大小: 5
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_21
【通知事件】生成通知事件: 用户 user_21 接收 READ_POST 通知
【通知事件】为用户 user_21 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_21), 队列大小: 6
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_20
【通知事件】生成通知事件: 用户 user_20 接收 READ_POST 通知
【通知事件】为用户 user_20 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_20), 队列大小: 7
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_19
【通知事件】生成通知事件: 用户 user_19 接收 READ_POST 通知
【通知事件】为用户 user_19 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_19), 队列大小: 8
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_96
【通知事件】生成通知事件: 用户 user_96 接收 READ_POST 通知
【通知事件】为用户 user_96 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_96), 队列大小: 9
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_26
【通知事件】生成通知事件: 用户 user_26 接收 READ_POST 通知
2025-08-26 21:29:40,421 - Simulator - INFO - Added READ_POST event for user user_26 to queue. Queue size: 10
2025-08-26 21:29:40,421 - Simulator - INFO - Generated READ_POST event for user user_26 based on notification.
2025-08-26 21:29:40,422 - NotificationModule - INFO - Would send notification to user 'user_88': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,429 - Simulator - INFO - Added READ_POST event for user user_88 to queue. Queue size: 11
2025-08-26 21:29:40,429 - Simulator - INFO - Generated READ_POST event for user user_88 based on notification.
2025-08-26 21:29:40,429 - NotificationModule - INFO - Would send notification to user 'user_77': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,437 - Simulator - INFO - Added READ_POST event for user user_77 to queue. Queue size: 12
2025-08-26 21:29:40,437 - Simulator - INFO - Generated READ_POST event for user user_77 based on notification.
2025-08-26 21:29:40,437 - NotificationModule - INFO - Would send notification to user 'user_11': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,445 - Simulator - INFO - Added READ_POST event for user user_11 to queue. Queue size: 13
2025-08-26 21:29:40,445 - Simulator - INFO - Generated READ_POST event for user user_11 based on notification.
2025-08-26 21:29:40,446 - NotificationModule - INFO - Would send notification to user 'user_71': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,453 - Simulator - INFO - Added READ_POST event for user user_71 to queue. Queue size: 14
2025-08-26 21:29:40,453 - Simulator - INFO - Generated READ_POST event for user user_71 based on notification.
2025-08-26 21:29:40,454 - NotificationModule - INFO - Would send notification to user 'user_12': User 'user_53' you follow has published a new post 'post_632c077b'.
2025-08-26 21:29:40,461 - Simulator - INFO - Added READ_POST event for user user_12 to queue. Queue size: 15
2025-08-26 21:29:40,461 - Simulator - INFO - Generated READ_POST event for user user_12 based on notification.
2025-08-26 21:29:40,462 - Engine - INFO - Sent new post notification to 15 followers.
2025-08-26 21:29:40,462 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_53'...
2025-08-26 21:29:40,462 - src.modules.belief_module - INFO - 开始处理用户 'user_53' 的认知流程...
2025-08-26 21:29:40,486 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:29:40,487 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:29:40,492 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:29:40,492 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_81621c53' 更新或创建信念...
2025-08-26 21:29:40,580 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 1), 最大相似度: 0.3387
2025-08-26 21:29:40,580 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756214187004530'
2025-08-26 21:29:40,581 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近跟家人聊结婚和分家产的事，突然觉得以前啥都摆在桌上说的那套，现在可能不太安全了。爸妈那辈人习惯把户口本、房产证、银行流水都拿出来聊，可现在这些信息要是被泄露，后果可不止闹矛盾这么简单。我看到个例子，有人婚前聊天记录被拿去当财产分割的证据，吓一跳。现在结婚讲信任，但信任也得有数据安全做底子。我劝家里人，重要的财务和身份信息别随便拍照发群里，也别随便给那些“家庭管理”APP授权。不是不信任人，是数据一出去就收不回来了。保护隐私不是冷淡，其实是对家人更负责。'
2025-08-26 21:29:40,581 - src.modules.belief_module - INFO - embedding相似度 (0.3387) 低于阈值 (0.9)，创建节点
2025-08-26 21:29:40,581 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:29:40,581 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_81621c53' 的证据（多线程模式）...
2025-08-26 21:29:45,692 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.6907, 反对权重: 0.0000
2025-08-26 21:29:45,693 - src.modules.belief_module - INFO - 正在为用户 'user_53' 创建新信念: '最近跟家人聊结婚和分家产的事，突然觉得以前啥都摆在桌上说的那...'
2025-08-26 21:29:50,595 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:29:50,595 - src.modules.belief_module - INFO - 正在为信念 'belief_user_53_c2be104d' 建立关系网络（多线程模式）...
2025-08-26 21:29:50,611 - src.modules.belief_module - INFO - 找到 4 个现有信念，开始多线程处理...
2025-08-26 21:30:01,073 - src.modules.belief_module - INFO - 建立关系 'belief_user_53_c2be104d' -反驳/diminishes-> 'belief_1756214187004530' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:30:06,019 - src.modules.belief_module - INFO - 建立关系 'belief_1756214187004530' -支持/is_example_of-> 'belief_user_53_c2be104d' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:30:09,551 - src.modules.belief_module - INFO - 建立关系 'belief_1756214187004531' -反驳/diminishes-> 'belief_user_53_c2be104d' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:30:12,775 - src.modules.belief_module - INFO - 为信念 'belief_user_53_c2be104d' 建立了 1 个关系（多线程模式）
2025-08-26 21:30:12,775 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_53_c2be104d':
2025-08-26 21:30:12,775 - src.modules.belief_module - INFO -   - 真实度: 0.6907
2025-08-26 21:30:12,775 - src.modules.belief_module - INFO -   - 置信度: 0.3290
2025-08-26 21:30:12,779 - src.modules.belief_module - INFO - 信念 'belief_user_53_c2be104d' 的置信度 (0.3290) 低于求证阈值 (0.4500)
2025-08-26 21:30:12,779 - src.modules.belief_module - INFO - 正在为用户 'user_53' 触发对信念 'belief_user_53_c2be104d' 的求证机制...
2025-08-26 21:30:12,779 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 搜索结果:
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 1. 这个谣言虽然是假的，但它能火爆全网，恰恰说明了一个现实问题：我国对非婚同居关系的法律保护确实存在空白。现实中，一方为家庭付出多年，但因没有一纸婚书，分手或一方离世后权益无法保障的案例比比皆是。我们不应止于辟谣，更应思考，如何在保护私有财产权和维护传统婚姻制度之间，为“事实伴侣”提供一条人道的、有限的救济途径？比如在分割同居期间共同财产（析产）或请求扶养补偿方面，是否可以有更明确的规定？#法律思考 #非婚同居 #民法典
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 2. 大家别慌，我们国家的法律基石是很稳固的。关于财产，尤其是房子这种大事，核心就一条：不动产登记簿上写谁，就是谁的。这叫物权公示公信原则。别说同居一年，就是同居十年，只要房本没你名，法律上你就没份。所有让你焦虑的说法，都绕不开这个基本原则。所以，守好你的产权证，比什么都强。#物权法 #不动产登记 #硬核知识
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 生成了求证搜索行为: '理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境'
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-26 21:30:12,780 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-26 21:30:12,780 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境'
2025-08-26 21:30:12,781 - src.modules.belief_module - INFO - 认知冲击 (0.6907) 超过怀疑阈值 (0.3700)
2025-08-26 21:30:12,781 - src.modules.agent_module - INFO - 正在为用户 'user_53' 演化特质，认知冲击为: 0.6906927625791831, 内容情感强度: 0.4062499999999999
2025-08-26 21:30:12,783 - src.modules.agent_module - INFO - 基于内容情感强度 0.406 调整情绪波动性变化: -0.0104
2025-08-26 21:30:15,858 - src.modules.agent_module - INFO - 用户 'user_53' 的特质已更新:
2025-08-26 21:30:15,858 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.4391
2025-08-26 21:30:15,859 - src.modules.agent_module - INFO -   - 验证阈值: 0.4845
2025-08-26 21:30:15,859 - src.modules.agent_module - INFO -   - 情绪波动性: 0.3496
2025-08-26 21:30:15,859 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_53_c2be104d' 的变化，认知冲击为 0.6906927625791831...
2025-08-26 21:30:15,859 - src.modules.belief_module - INFO - 信念 'belief_user_53_c2be104d' 没有关联边，无需传播
2025-08-26 21:30:15,859 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.6907, 情感强度: 0.406
2025-08-26 21:30:15,859 - src.modules.belief_module - INFO - 将来自簇 cluster_81621c53 的 2 条记忆移入历史...
2025-08-26 21:30:27,132 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:30:27,134 - Engine - INFO - Cognitive processing for user 'user_53' finished.
2025-08-26 21:30:27,134 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-26 21:30:27,134 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-26 21:30:27,134 - Engine - INFO - Processing queued verification action for user 'user_53'
2025-08-26 21:30:27,134 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_53' -----
2025-08-26 21:30:35,585 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_53_637d8816).
2025-08-26 21:30:35,585 - Engine - INFO - No target_id provided for a content update action that requires one.
2025-08-26 21:30:35,586 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:30:35,586 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:30:35,586 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_53'...
2025-08-26 21:30:35,586 - src.modules.belief_module - INFO - 开始处理用户 'user_53' 的认知流程...
2025-08-26 21:30:35,601 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:30:35,601 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:30:35,601 - src.modules.belief_module - INFO - 用户 'user_53' 的短期记忆中未找到显著议题簇
2025-08-26 21:30:35,601 - Engine - INFO - Cognitive processing for user 'user_53' finished.
2025-08-26 21:30:35,602 - Engine - INFO - ----- Action 'READ_POST' for user 'user_53' processed successfully. -----
2025-08-26 21:30:35,602 - Engine - INFO - Queued verification action processed successfully
2025-08-26 21:30:35,602 - Engine - INFO - All verification actions processed.
2025-08-26 21:30:35,602 - Engine - INFO - Verification queue processing finished.
2025-08-26 21:30:35,602 - Engine - INFO - ----- Action 'CREATE_POST' for user 'user_53' processed successfully. -----
2025-08-26 21:30:35,603 - Simulator - INFO - Processing event from queue: READ_POST for user user_73
2025-08-26 21:30:35,603 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_73' -----
2025-08-26 21:30:42,907 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_73_642a8120).
2025-08-26 21:30:42,907 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:30:46,165 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:30:46,165 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:30:46,165 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:30:46,165 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_73'...
2025-08-26 21:30:46,165 - src.modules.belief_module - INFO - 开始处理用户 'user_73' 的认知流程...
2025-08-26 21:30:46,180 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:30:46,180 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:30:46,180 - src.modules.belief_module - INFO - 用户 'user_73' 的短期记忆中未找到显著议题簇
2025-08-26 21:30:46,181 - Engine - INFO - Cognitive processing for user 'user_73' finished.
2025-08-26 21:30:46,181 - Engine - INFO - ----- Action 'READ_POST' for user 'user_73' processed successfully. -----
2025-08-26 21:30:46,181 - Simulator - INFO - Processing event from queue: READ_POST for user user_49
2025-08-26 21:30:46,181 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_49' -----
2025-08-26 21:30:53,111 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_49_2053096d).
2025-08-26 21:30:53,111 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:30:55,889 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:30:55,890 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:30:55,890 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:30:55,890 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_49'...
2025-08-26 21:30:55,890 - src.modules.belief_module - INFO - 开始处理用户 'user_49' 的认知流程...
2025-08-26 21:30:55,905 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:30:55,905 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:30:55,905 - src.modules.belief_module - INFO - 用户 'user_49' 的短期记忆中未找到显著议题簇
2025-08-26 21:30:55,905 - Engine - INFO - Cognitive processing for user 'user_49' finished.
2025-08-26 21:30:55,905 - Engine - INFO - ----- Action 'READ_POST' for user 'user_49' processed successfully. -----
2025-08-26 21:30:55,906 - Simulator - INFO - Processing event from queue: READ_POST for user user_91
2025-08-26 21:30:55,906 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_91' -----
2025-08-26 21:31:03,020 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_91_a302c438).
2025-08-26 21:31:03,020 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:31:08,261 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:31:08,261 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:31:08,261 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:31:08,261 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_91'...
2025-08-26 21:31:08,261 - src.modules.belief_module - INFO - 开始处理用户 'user_91' 的认知流程...
【通知事件】为用户 user_26 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_26), 队列大小: 10
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_88
【通知事件】生成通知事件: 用户 user_88 接收 READ_POST 通知
【通知事件】为用户 user_88 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_88), 队列大小: 11
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_77
【通知事件】生成通知事件: 用户 user_77 接收 READ_POST 通知
【通知事件】为用户 user_77 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_77), 队列大小: 12
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_11
【通知事件】生成通知事件: 用户 user_11 接收 READ_POST 通知
【通知事件】为用户 user_11 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_11), 队列大小: 13
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_71
【通知事件】生成通知事件: 用户 user_71 接收 READ_POST 通知
【通知事件】为用户 user_71 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_71), 队列大小: 14
【通知模块】用户 user_53 发布了新帖子，发送通知给关注者 user_12
【通知事件】生成通知事件: 用户 user_12 接收 READ_POST 通知
【通知事件】为用户 user_12 生成浏览事件，浏览帖子 post_632c077b："最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我直摇头。说真的，规定是规定，谁家结婚前不签个协议..."
【事件队列】添加事件: READ_POST (用户 user_12), 队列大小: 15
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_53 的认知处理...

【认知处理】开始处理用户 user_53 的认知流程
【阶段1+2】获取用户 user_53 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_53'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_53_c2be104d
【步骤 4/4】完成: 用户 user_53 的认知处理完成
【步骤 5/5】处理求证队列，队列大小: 1
【引擎】开始处理用户 user_53 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_53'...
MemoryModule: Successfully created memory 'mem_user_53_637d8816'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_53_637d8816)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_53 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_53 的认知处理...

【认知处理】开始处理用户 user_53 的认知流程
【阶段1+2】获取用户 user_53 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_53'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_53 的认知处理完成
【引擎】用户 user_53 的 READ_POST 操作处理成功
【步骤 5/5】完成: 求证队列处理完成
【引擎】用户 user_53 的 CREATE_POST 操作处理成功
【处理成功】事件处理成功: CREATE_POST (用户 user_53)
【处理事件】从队列中处理事件: READ_POST (用户 user_73)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_73 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_73'...
MemoryModule: Successfully created memory 'mem_user_73_642a8120'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_73_642a8120)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_73 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_73 的认知处理...

【认知处理】开始处理用户 user_73 的认知流程
【阶段1+2】获取用户 user_73 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_73'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_73 的认知处理完成
【引擎】用户 user_73 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_73)
【处理事件】从队列中处理事件: READ_POST (用户 user_49)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_49 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_49'...
MemoryModule: Successfully created memory 'mem_user_49_2053096d'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_49_2053096d)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_49 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_49 的认知处理...

【认知处理】开始处理用户 user_49 的认知流程
【阶段1+2】获取用户 user_49 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_49'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_49 的认知处理完成
【引擎】用户 user_49 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_49)
【处理事件】从队列中处理事件: READ_POST (用户 user_91)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_91 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_91'...
MemoryModule: Successfully created memory 'mem_user_91_a302c438'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_91_a302c438)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_91 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_91 的认知处理...

【认知处理】开始处理用户 user_91 的认知流程
【阶段1+2】获取用户 user_91 的短期记忆队列
2025-08-26 21:31:08,276 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:31:08,276 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:31:08,277 - src.modules.belief_module - INFO - 用户 'user_91' 的短期记忆中未找到显著议题簇
2025-08-26 21:31:08,277 - Engine - INFO - Cognitive processing for user 'user_91' finished.
2025-08-26 21:31:08,277 - Engine - INFO - ----- Action 'READ_POST' for user 'user_91' processed successfully. -----
2025-08-26 21:31:08,277 - Simulator - INFO - Processing event from queue: READ_POST for user user_54
2025-08-26 21:31:08,278 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_54' -----
2025-08-26 21:31:15,163 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_54_716bf402).
2025-08-26 21:31:15,163 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:31:17,883 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:31:17,884 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:31:17,884 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:31:17,884 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_54'...
2025-08-26 21:31:17,884 - src.modules.belief_module - INFO - 开始处理用户 'user_54' 的认知流程...
2025-08-26 21:31:17,898 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:31:17,899 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:31:17,899 - src.modules.belief_module - INFO - 用户 'user_54' 的短期记忆中未找到显著议题簇
2025-08-26 21:31:17,899 - Engine - INFO - Cognitive processing for user 'user_54' finished.
2025-08-26 21:31:17,899 - Engine - INFO - ----- Action 'READ_POST' for user 'user_54' processed successfully. -----
2025-08-26 21:31:17,900 - Simulator - INFO - Processing event from queue: READ_POST for user user_24
2025-08-26 21:31:17,900 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_24' -----
2025-08-26 21:31:25,050 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_24_eb67622f).
2025-08-26 21:31:25,050 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:31:28,583 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:31:28,583 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:31:28,583 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:31:28,583 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_24'...
2025-08-26 21:31:28,584 - src.modules.belief_module - INFO - 开始处理用户 'user_24' 的认知流程...
2025-08-26 21:31:28,608 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:31:28,608 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:31:28,613 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:31:28,613 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_3b459db7' 更新或创建信念...
2025-08-26 21:31:28,679 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 1), 最大相似度: 0.2967
2025-08-26 21:31:28,679 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756213818156548'
2025-08-26 21:31:28,680 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近跟家人聊结婚和分家产的事，突然发现以前啥都敢晒、啥都敢说，现在可能有风险。以前户口本、房产证、银行流水都摆在桌上聊，现在这些信息要是被泄露，麻烦可大了。听说有人婚前聊天记录被拿去当财产分割的证据，真是吓一跳。现在结婚讲信任，但得先有数据安全。我劝家里人，重要的财务和身份信息别随便拍照发群里，也别乱授权给什么“家庭管理”APP。不是不信任人，是数据一出去就收不回来了。保护隐私，其实是为了更好地对家人负责。'
2025-08-26 21:31:28,680 - src.modules.belief_module - INFO - embedding相似度 (0.2967) 低于阈值 (0.9)，创建节点
2025-08-26 21:31:28,680 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:31:28,680 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_3b459db7' 的证据（多线程模式）...
2025-08-26 21:31:34,199 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.6438, 反对权重: 0.0000
2025-08-26 21:31:34,199 - src.modules.belief_module - INFO - 正在为用户 'user_24' 创建新信念: '最近跟家人聊结婚和分家产的事，突然发现以前啥都敢晒、啥都敢说...'
2025-08-26 21:31:37,625 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:31:37,626 - src.modules.belief_module - INFO - 正在为信念 'belief_user_24_6c112a55' 建立关系网络（多线程模式）...
2025-08-26 21:31:37,644 - src.modules.belief_module - INFO - 找到 5 个现有信念，开始多线程处理...
2025-08-26 21:31:52,093 - src.modules.belief_module - INFO - 建立关系 'belief_1756213818156548' -支持/imply-> 'belief_user_24_6c112a55' (权重: 0.75, 置信度: 0.87)
2025-08-26 21:31:55,550 - src.modules.belief_module - INFO - 建立关系 'belief_1756213818156545' -支持/imply-> 'belief_user_24_6c112a55' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:31:58,950 - src.modules.belief_module - INFO - 建立关系 'belief_1756213818156547' -支持/is_evidence_for-> 'belief_user_24_6c112a55' (权重: 0.75, 置信度: 0.87)
2025-08-26 21:32:02,282 - src.modules.belief_module - INFO - 建立关系 'belief_1756213818156549' -支持/imply-> 'belief_user_24_6c112a55' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:32:02,282 - src.modules.belief_module - INFO - 信念 'belief_user_24_6c112a55' 没有与其他信念建立关系（多线程模式）
2025-08-26 21:32:02,283 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_24_6c112a55':
2025-08-26 21:32:02,283 - src.modules.belief_module - INFO -   - 真实度: 0.6438
2025-08-26 21:32:02,283 - src.modules.belief_module - INFO -   - 置信度: 0.3066
2025-08-26 21:32:02,286 - src.modules.belief_module - INFO - 信念 'belief_user_24_6c112a55' 的置信度 (0.3066) 低于求证阈值 (0.6800)
2025-08-26 21:32:02,287 - src.modules.belief_module - INFO - 正在为用户 'user_24' 触发对信念 'belief_user_24_6c112a55' 的求证机制...
2025-08-26 21:32:02,287 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境
2025-08-26 21:32:02,287 - src.modules.belief_module - INFO - 搜索结果:
2025-08-26 21:32:02,287 - src.modules.belief_module - INFO - 1. 这个谣言虽然是假的，但它能火爆全网，恰恰说明了一个现实问题：我国对非婚同居关系的法律保护确实存在空白。现实中，一方为家庭付出多年，但因没有一纸婚书，分手或一方离世后权益无法保障的案例比比皆是。我们不应止于辟谣，更应思考，如何在保护私有财产权和维护传统婚姻制度之间，为“事实伴侣”提供一条人道的、有限的救济途径？比如在分割同居期间共同财产（析产）或请求扶养补偿方面，是否可以有更明确的规定？#法律思考 #非婚同居 #民法典
2025-08-26 21:32:02,287 - src.modules.belief_module - INFO - 2. 跟爸妈讲清楚了：1. 没领证就不叫“配偶”，没资格按夫妻继承。2. 遗嘱的效力最大，您想给谁就给谁，别人抢不走。3. 房本上是您的名字，那就是您的，谁也拿不走。谣言就是利用老年人信息差和爱子心切的心理，让他们焦虑。大家也快去跟自己爸妈科普一下吧！#家庭辟谣 #中老年关怀 #民法典常识
2025-08-26 21:32:02,287 - src.modules.belief_module - INFO - 生成了求证搜索行为: '理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境'
2025-08-26 21:32:02,288 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-26 21:32:02,288 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-26 21:32:02,288 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-26 21:32:02,288 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境'
2025-08-26 21:32:02,288 - src.modules.belief_module - INFO - 认知冲击 (0.6438) 超过怀疑阈值 (0.5300)
2025-08-26 21:32:02,288 - src.modules.agent_module - INFO - 正在为用户 'user_24' 演化特质，认知冲击为: 0.6437727625791831, 内容情感强度: 0.4062499999999999
2025-08-26 21:32:02,291 - src.modules.agent_module - INFO - 基于内容情感强度 0.406 调整情绪波动性变化: -0.0097
2025-08-26 21:32:05,401 - src.modules.agent_module - INFO - 用户 'user_24' 的特质已更新:
2025-08-26 21:32:05,401 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.5944
2025-08-26 21:32:05,401 - src.modules.agent_module - INFO -   - 验证阈值: 0.7122
2025-08-26 21:32:05,401 - src.modules.agent_module - INFO -   - 情绪波动性: 0.4103
2025-08-26 21:32:05,401 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_24_6c112a55' 的变化，认知冲击为 0.6437727625791831...
2025-08-26 21:32:05,402 - src.modules.belief_module - INFO - 信念 'belief_user_24_6c112a55' 没有关联边，无需传播
2025-08-26 21:32:05,402 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.6438, 情感强度: 0.406
2025-08-26 21:32:05,402 - src.modules.belief_module - INFO - 将来自簇 cluster_3b459db7 的 2 条记忆移入历史...
2025-08-26 21:32:19,004 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:32:19,006 - Engine - INFO - Cognitive processing for user 'user_24' finished.
2025-08-26 21:32:19,006 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-26 21:32:19,006 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-26 21:32:19,006 - Engine - INFO - Processing queued verification action for user 'user_24'
2025-08-26 21:32:19,006 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_24' -----
2025-08-26 21:32:25,725 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_24_32cedbd1).
2025-08-26 21:32:25,725 - Engine - INFO - No target_id provided for a content update action that requires one.
2025-08-26 21:32:25,725 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:32:25,725 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:32:25,725 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_24'...
2025-08-26 21:32:25,726 - src.modules.belief_module - INFO - 开始处理用户 'user_24' 的认知流程...
2025-08-26 21:32:25,818 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:32:25,818 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:32:25,818 - src.modules.belief_module - INFO - 用户 'user_24' 的短期记忆中未找到显著议题簇
2025-08-26 21:32:25,818 - Engine - INFO - Cognitive processing for user 'user_24' finished.
2025-08-26 21:32:25,818 - Engine - INFO - ----- Action 'READ_POST' for user 'user_24' processed successfully. -----
2025-08-26 21:32:25,819 - Engine - INFO - Queued verification action processed successfully
2025-08-26 21:32:25,819 - Engine - INFO - All verification actions processed.
2025-08-26 21:32:25,819 - Engine - INFO - Verification queue processing finished.
2025-08-26 21:32:25,819 - Engine - INFO - ----- Action 'READ_POST' for user 'user_24' processed successfully. -----
2025-08-26 21:32:25,820 - Simulator - INFO - Processing event from queue: READ_POST for user user_21
2025-08-26 21:32:25,820 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_21' -----
2025-08-26 21:32:33,369 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_21_93775569).
2025-08-26 21:32:33,369 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:32:36,459 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:32:36,460 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:32:36,460 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:32:36,460 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_21'...
2025-08-26 21:32:36,460 - src.modules.belief_module - INFO - 开始处理用户 'user_21' 的认知流程...
2025-08-26 21:32:36,472 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:32:36,472 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:32:36,472 - src.modules.belief_module - INFO - 用户 'user_21' 的短期记忆中未找到显著议题簇
2025-08-26 21:32:36,473 - Engine - INFO - Cognitive processing for user 'user_21' finished.
2025-08-26 21:32:36,473 - Engine - INFO - ----- Action 'READ_POST' for user 'user_21' processed successfully. -----
2025-08-26 21:32:36,473 - Simulator - INFO - Processing event from queue: READ_POST for user user_20
2025-08-26 21:32:36,473 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_20' -----
2025-08-26 21:32:43,967 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_20_484b84fc).
2025-08-26 21:32:43,968 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:32:46,847 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:32:46,847 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:32:46,848 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:32:46,848 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_20'...
2025-08-26 21:32:46,848 - src.modules.belief_module - INFO - 开始处理用户 'user_20' 的认知流程...
2025-08-26 21:32:46,862 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:32:46,863 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:32:46,863 - src.modules.belief_module - INFO - 用户 'user_20' 的短期记忆中未找到显著议题簇
2025-08-26 21:32:46,863 - Engine - INFO - Cognitive processing for user 'user_20' finished.
2025-08-26 21:32:46,863 - Engine - INFO - ----- Action 'READ_POST' for user 'user_20' processed successfully. -----
2025-08-26 21:32:46,864 - Simulator - INFO - Processing event from queue: READ_POST for user user_19
2025-08-26 21:32:46,864 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_19' -----
2025-08-26 21:32:54,128 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_19_ba49e634).
2025-08-26 21:32:54,129 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:32:57,003 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:32:57,003 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:32:57,003 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:32:57,003 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_19'...
2025-08-26 21:32:57,003 - src.modules.belief_module - INFO - 开始处理用户 'user_19' 的认知流程...
2025-08-26 21:32:57,018 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:32:57,018 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:32:57,018 - src.modules.belief_module - INFO - 用户 'user_19' 的短期记忆中未找到显著议题簇
2025-08-26 21:32:57,019 - Engine - INFO - Cognitive processing for user 'user_19' finished.
2025-08-26 21:32:57,019 - Engine - INFO - ----- Action 'READ_POST' for user 'user_19' processed successfully. -----
MemoryModule: Getting STM queue for user 'user_91'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_91 的认知处理完成
【引擎】用户 user_91 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_91)
【处理事件】从队列中处理事件: READ_POST (用户 user_54)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_54 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_54'...
MemoryModule: Successfully created memory 'mem_user_54_716bf402'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_54_716bf402)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_54 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_54 的认知处理...

【认知处理】开始处理用户 user_54 的认知流程
【阶段1+2】获取用户 user_54 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_54'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_54 的认知处理完成
【引擎】用户 user_54 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_54)
【处理事件】从队列中处理事件: READ_POST (用户 user_24)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_24 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_24'...
MemoryModule: Successfully created memory 'mem_user_24_eb67622f'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_24_eb67622f)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_24 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_24 的认知处理...

【认知处理】开始处理用户 user_24 的认知流程
【阶段1+2】获取用户 user_24 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_24'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_24_6c112a55
【步骤 4/4】完成: 用户 user_24 的认知处理完成
【步骤 5/5】处理求证队列，队列大小: 1
【引擎】开始处理用户 user_24 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_24'...
MemoryModule: Successfully created memory 'mem_user_24_32cedbd1'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_24_32cedbd1)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_24 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_24 的认知处理...

【认知处理】开始处理用户 user_24 的认知流程
【阶段1+2】获取用户 user_24 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_24'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_24 的认知处理完成
【引擎】用户 user_24 的 READ_POST 操作处理成功
【步骤 5/5】完成: 求证队列处理完成
【引擎】用户 user_24 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_24)
【处理事件】从队列中处理事件: READ_POST (用户 user_21)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_21 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_21'...
MemoryModule: Successfully created memory 'mem_user_21_93775569'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_21_93775569)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_21 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_21 的认知处理...

【认知处理】开始处理用户 user_21 的认知流程
【阶段1+2】获取用户 user_21 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_21'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_21 的认知处理完成
【引擎】用户 user_21 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_21)
【处理事件】从队列中处理事件: READ_POST (用户 user_20)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_20 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_20'...
MemoryModule: Successfully created memory 'mem_user_20_484b84fc'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_20_484b84fc)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_20 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_20 的认知处理...

【认知处理】开始处理用户 user_20 的认知流程
【阶段1+2】获取用户 user_20 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_20'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_20 的认知处理完成
【引擎】用户 user_20 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_20)
【处理事件】从队列中处理事件: READ_POST (用户 user_19)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_19 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_19'...
MemoryModule: Successfully created memory 'mem_user_19_ba49e634'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_19_ba49e634)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_19 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_19 的认知处理...

【认知处理】开始处理用户 user_19 的认知流程
【阶段1+2】获取用户 user_19 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_19'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_19 的认知处理完成
【引擎】用户 user_19 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_19)
2025-08-26 21:32:57,019 - Simulator - INFO - Processing event from queue: READ_POST for user user_96
2025-08-26 21:32:57,020 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_96' -----
2025-08-26 21:33:04,280 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_96_a6e58190).
2025-08-26 21:33:04,281 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:33:07,052 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:33:07,052 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:33:07,052 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:33:07,052 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_96'...
2025-08-26 21:33:07,053 - src.modules.belief_module - INFO - 开始处理用户 'user_96' 的认知流程...
2025-08-26 21:33:07,067 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:33:07,067 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:33:07,068 - src.modules.belief_module - INFO - 用户 'user_96' 的短期记忆中未找到显著议题簇
2025-08-26 21:33:07,068 - Engine - INFO - Cognitive processing for user 'user_96' finished.
2025-08-26 21:33:07,068 - Engine - INFO - ----- Action 'READ_POST' for user 'user_96' processed successfully. -----
2025-08-26 21:33:07,068 - Simulator - INFO - Processing event from queue: READ_POST for user user_26
2025-08-26 21:33:07,069 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_26' -----
2025-08-26 21:33:14,184 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_26_26036198).
2025-08-26 21:33:14,184 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:33:17,130 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:33:17,130 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:33:17,130 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:33:17,130 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_26'...
2025-08-26 21:33:17,130 - src.modules.belief_module - INFO - 开始处理用户 'user_26' 的认知流程...
2025-08-26 21:33:17,145 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:33:17,145 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:33:17,145 - src.modules.belief_module - INFO - 用户 'user_26' 的短期记忆中未找到显著议题簇
2025-08-26 21:33:17,146 - Engine - INFO - Cognitive processing for user 'user_26' finished.
2025-08-26 21:33:17,146 - Engine - INFO - ----- Action 'READ_POST' for user 'user_26' processed successfully. -----
2025-08-26 21:33:17,146 - Simulator - INFO - Processing event from queue: READ_POST for user user_88
2025-08-26 21:33:17,146 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_88' -----
2025-08-26 21:33:24,810 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_88_f8f2e6a2).
2025-08-26 21:33:24,810 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:33:27,673 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:33:27,673 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:33:27,673 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:33:27,674 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_88'...
2025-08-26 21:33:27,674 - src.modules.belief_module - INFO - 开始处理用户 'user_88' 的认知流程...
2025-08-26 21:33:27,688 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:33:27,689 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:33:27,689 - src.modules.belief_module - INFO - 用户 'user_88' 的短期记忆中未找到显著议题簇
2025-08-26 21:33:27,689 - Engine - INFO - Cognitive processing for user 'user_88' finished.
2025-08-26 21:33:27,689 - Engine - INFO - ----- Action 'READ_POST' for user 'user_88' processed successfully. -----
2025-08-26 21:33:27,690 - Simulator - INFO - Processing event from queue: READ_POST for user user_77
2025-08-26 21:33:27,690 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_77' -----
2025-08-26 21:33:35,309 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_77_d81eef89).
2025-08-26 21:33:35,309 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:33:38,297 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:33:38,297 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:33:38,297 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:33:38,297 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_77'...
2025-08-26 21:33:38,298 - src.modules.belief_module - INFO - 开始处理用户 'user_77' 的认知流程...
2025-08-26 21:33:38,312 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:33:38,312 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:33:38,312 - src.modules.belief_module - INFO - 用户 'user_77' 的短期记忆中未找到显著议题簇
2025-08-26 21:33:38,313 - Engine - INFO - Cognitive processing for user 'user_77' finished.
2025-08-26 21:33:38,313 - Engine - INFO - ----- Action 'READ_POST' for user 'user_77' processed successfully. -----
2025-08-26 21:33:38,313 - Simulator - INFO - Processing event from queue: READ_POST for user user_11
2025-08-26 21:33:38,313 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_11' -----
2025-08-26 21:33:45,393 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_11_0d63ab19).
2025-08-26 21:33:45,393 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:33:50,520 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:33:50,520 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:33:50,520 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:33:50,520 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_11'...
2025-08-26 21:33:50,520 - src.modules.belief_module - INFO - 开始处理用户 'user_11' 的认知流程...
2025-08-26 21:33:50,535 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:33:50,535 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:33:50,535 - src.modules.belief_module - INFO - 用户 'user_11' 的短期记忆中未找到显著议题簇
2025-08-26 21:33:50,536 - Engine - INFO - Cognitive processing for user 'user_11' finished.
2025-08-26 21:33:50,536 - Engine - INFO - ----- Action 'READ_POST' for user 'user_11' processed successfully. -----
2025-08-26 21:33:50,536 - Simulator - INFO - Processing event from queue: READ_POST for user user_71
2025-08-26 21:33:50,536 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_71' -----
2025-08-26 21:33:57,519 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_71_29428e99).
2025-08-26 21:33:57,519 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:34:00,291 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:34:00,292 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:34:00,292 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:34:00,292 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_71'...
2025-08-26 21:34:00,292 - src.modules.belief_module - INFO - 开始处理用户 'user_71' 的认知流程...
2025-08-26 21:34:00,307 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:34:00,307 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:34:00,307 - src.modules.belief_module - INFO - 用户 'user_71' 的短期记忆中未找到显著议题簇
2025-08-26 21:34:00,307 - Engine - INFO - Cognitive processing for user 'user_71' finished.
2025-08-26 21:34:00,307 - Engine - INFO - ----- Action 'READ_POST' for user 'user_71' processed successfully. -----
2025-08-26 21:34:00,308 - Simulator - INFO - Processing event from queue: READ_POST for user user_12
2025-08-26 21:34:00,308 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_12' -----
【处理事件】从队列中处理事件: READ_POST (用户 user_96)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_96 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_96'...
MemoryModule: Successfully created memory 'mem_user_96_a6e58190'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_96_a6e58190)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_96 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_96 的认知处理...

【认知处理】开始处理用户 user_96 的认知流程
【阶段1+2】获取用户 user_96 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_96'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_96 的认知处理完成
【引擎】用户 user_96 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_96)
【处理事件】从队列中处理事件: READ_POST (用户 user_26)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_26 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_26'...
MemoryModule: Successfully created memory 'mem_user_26_26036198'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_26_26036198)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_26 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_26 的认知处理...

【认知处理】开始处理用户 user_26 的认知流程
【阶段1+2】获取用户 user_26 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_26'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_26 的认知处理完成
【引擎】用户 user_26 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_26)
【处理事件】从队列中处理事件: READ_POST (用户 user_88)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_88 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_88'...
MemoryModule: Successfully created memory 'mem_user_88_f8f2e6a2'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_88_f8f2e6a2)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_88 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_88 的认知处理...

【认知处理】开始处理用户 user_88 的认知流程
【阶段1+2】获取用户 user_88 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_88'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_88 的认知处理完成
【引擎】用户 user_88 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_88)
【处理事件】从队列中处理事件: READ_POST (用户 user_77)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_77 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_77'...
MemoryModule: Successfully created memory 'mem_user_77_d81eef89'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_77_d81eef89)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_77 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_77 的认知处理...

【认知处理】开始处理用户 user_77 的认知流程
【阶段1+2】获取用户 user_77 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_77'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_77 的认知处理完成
【引擎】用户 user_77 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_77)
【处理事件】从队列中处理事件: READ_POST (用户 user_11)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_11 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_11'...
MemoryModule: Successfully created memory 'mem_user_11_0d63ab19'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_11_0d63ab19)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_11 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_11 的认知处理...

【认知处理】开始处理用户 user_11 的认知流程
【阶段1+2】获取用户 user_11 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_11'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_11 的认知处理完成
【引擎】用户 user_11 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_11)
【处理事件】从队列中处理事件: READ_POST (用户 user_71)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_71 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_71'...
MemoryModule: Successfully created memory 'mem_user_71_29428e99'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_71_29428e99)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_71 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_71 的认知处理...

【认知处理】开始处理用户 user_71 的认知流程
【阶段1+2】获取用户 user_71 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_71'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_71 的认知处理完成
【引擎】用户 user_71 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_71)
【处理事件】从队列中处理事件: READ_POST (用户 user_12)，浏览帖子 post_632c077b："您关注的用户 user_53 发布了新帖子:

最近朋友圈都在聊婚姻财产公证、继承条款这些事，看得我..."
【引擎】开始处理用户 user_12 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
2025-08-26 21:34:07,872 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_12_b5f4bc42).
2025-08-26 21:34:07,873 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:34:10,669 - src.modules.content_module - INFO - Incremented view count for post 'post_632c077b'.
2025-08-26 21:34:10,669 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:34:10,669 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:34:10,669 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_12'...
2025-08-26 21:34:10,670 - src.modules.belief_module - INFO - 开始处理用户 'user_12' 的认知流程...
2025-08-26 21:34:10,684 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:34:10,684 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:34:10,685 - src.modules.belief_module - INFO - 用户 'user_12' 的短期记忆中未找到显著议题簇
2025-08-26 21:34:10,685 - Engine - INFO - Cognitive processing for user 'user_12' finished.
2025-08-26 21:34:10,685 - Engine - INFO - ----- Action 'READ_POST' for user 'user_12' processed successfully. -----
2025-08-26 21:34:10,685 - Simulator - INFO - Simulation step completed for user user_53.
2025-08-26 21:34:10,686 - Simulator - INFO - Simulation completed with 5 steps.
2025-08-26 21:34:14,527 - BeliefLogger - INFO - 已记录轮次 1 after 阶段的所有用户信念到 round_1_after_beliefs.json
2025-08-26 21:34:15,142 - BeliefLogger - INFO - 已记录轮次 1 after 阶段的所有用户记忆到 round_1_after_memories.json
2025-08-26 21:34:15,434 - BeliefLogger - INFO - 已记录轮次 1 after 阶段的所有用户信息到 round_1_after_users.json，共 100 个用户
2025-08-26 21:34:15,514 - ExperimentLoggers - INFO - 已记录轮次 1 after 阶段的所有帖子、评论和回复到 round_1_after_posts.json
2025-08-26 21:34:15,526 - ExperimentLoggers - INFO - 已记录轮次 1 after 阶段的所有评论和回复到 round_1_after_comments.json，共 33 条
2025-08-26 21:34:15,527 - ExperimentLogger - INFO - 开始记录轮次 1 after 阶段的实验指标...
2025-08-26 21:34:40,111 - ExperimentLogger - INFO - 已记录轮次 1 after 阶段的SIR指标到 round_1_after_sir_metrics.json
2025-08-26 21:34:40,111 - ExperimentLogger - INFO -   - 计算方法: 基于用户发言内容的谣言检测（符合SIR状态转换规则）
2025-08-26 21:34:40,111 - ExperimentLogger - INFO -   - 总体易感者: 93 (0.930) - 从未发布谣言内容
2025-08-26 21:34:40,111 - ExperimentLogger - INFO -   - 总体感染者: 6 (0.060) - 发布过谣言但未发布非谣言
2025-08-26 21:34:40,111 - ExperimentLogger - INFO -   - 总体康复者: 1 (0.010) - 曾经是感染者且后来发布了非谣言
2025-08-26 21:34:40,112 - ExperimentLogger - INFO -   - 知识分子群体SIR指标: 总数=0, 易感者=0(0.000), 感染者=0(0.000), 康复者=0(0.000)
2025-08-26 21:34:40,112 - ExperimentLogger - INFO -   - 普通群众群体SIR指标: 总数=100, 易感者=93(0.930), 感染者=6(0.060), 康复者=1(0.010)
2025-08-26 21:34:44,217 - ExperimentLogger - INFO - 已记录轮次 1 after 阶段的极化指数到 round_1_after_polarization_metrics.json
2025-08-26 21:34:44,217 - ExperimentLogger - INFO -   - 总体OEI指数: 0.210
2025-08-26 21:34:44,217 - ExperimentLogger - INFO -   - 极端观点用户: 21 (0.210)
2025-08-26 21:34:44,217 - ExperimentLogger - INFO -   - 温和观点用户: 2 (0.020)
2025-08-26 21:34:44,217 - ExperimentLogger - INFO -   - 无明确观点用户: 77 (0.770)
2025-08-26 21:34:44,217 - ExperimentLogger - INFO -   - 知识分子群体OEI指数: 0.000 (总数: 0, 极端: 0, 温和: 0, 无观点: 0)
2025-08-26 21:34:44,217 - ExperimentLogger - INFO -   - 普通群众群体OEI指数: 0.210 (总数: 100, 极端: 21, 温和: 2, 无观点: 77)
2025-08-26 21:34:48,571 - ExperimentLogger - INFO - 已记录轮次 1 after 阶段的智能体演化数据到 round_1_after_agent_evolution.json
2025-08-26 21:34:48,571 - ExperimentLogger - INFO -   - 追踪用户数: 100
2025-08-26 21:34:48,586 - ExperimentLogger - INFO - 已记录轮次 1 after 阶段的网络传播分析到 round_1_after_network_propagation.json
2025-08-26 21:34:48,586 - ExperimentLogger - INFO -   - 活跃用户数: 12
2025-08-26 21:34:48,586 - ExperimentLogger - INFO -   - 内容创作者数: 11
2025-08-26 21:34:48,586 - ExperimentLogger - INFO -   - 评论者数: 1
2025-08-26 21:34:48,586 - ExperimentLogger - INFO -   - 总帖子数: 12
2025-08-26 21:34:48,586 - ExperimentLogger - INFO -   - 总评论数: 33
2025-08-26 21:34:48,586 - ExperimentLogger - INFO - 轮次 1 after 阶段的实验指标记录完成
2025-08-26 21:34:48,594 - BeliefLogger - INFO - 已生成第 1 轮模拟的统计信息: round_1_summary.json
2025-08-26 21:34:48,598 - BeliefLogger - INFO - 已生成第 1 轮模拟的用户统计信息: round_1_users_summary.json
2025-08-26 21:34:48,598 - ExperimentLoggers - INFO - 已生成第 1 轮模拟的帖子统计信息: round_1_posts_summary.json
2025-08-26 21:34:48,599 - ExperimentLoggers - INFO - 已生成第 1 轮模拟的评论统计信息: round_1_comments_summary.json
2025-08-26 21:34:48,599 - Main - INFO - 第 1 轮模拟完成，信念、记忆和帖子已记录。
2025-08-26 21:34:48,599 - Main - INFO - 开始第 2/40 轮模拟
2025-08-26 21:34:52,322 - BeliefLogger - INFO - 已记录轮次 2 before 阶段的所有用户信念到 round_2_before_beliefs.json
2025-08-26 21:34:52,939 - BeliefLogger - INFO - 已记录轮次 2 before 阶段的所有用户记忆到 round_2_before_memories.json
2025-08-26 21:34:53,223 - BeliefLogger - INFO - 已记录轮次 2 before 阶段的所有用户信息到 round_2_before_users.json，共 100 个用户
2025-08-26 21:34:53,303 - ExperimentLoggers - INFO - 已记录轮次 2 before 阶段的所有帖子、评论和回复到 round_2_before_posts.json
2025-08-26 21:34:53,316 - ExperimentLoggers - INFO - 已记录轮次 2 before 阶段的所有评论和回复到 round_2_before_comments.json，共 33 条
2025-08-26 21:34:53,316 - ExperimentLogger - INFO - 跳过轮次 2 before 阶段的记录（与轮次 1 after 状态相同）
2025-08-26 21:34:53,316 - Simulator - INFO - 设置轮数信息: 当前轮次 2/40
2025-08-26 21:34:53,316 - Simulator - INFO - Starting simulation with 5 steps...
2025-08-26 21:34:53,316 - Simulator - INFO - Simulation step 1/5
2025-08-26 21:34:54,661 - Simulator - INFO - Added CREATE_COMMENT event for user user_87 to queue. Queue size: 1
2025-08-26 21:34:54,661 - Simulator - INFO - Processing event from queue: CREATE_COMMENT for user user_87
2025-08-26 21:34:54,661 - Engine - INFO - ----- Processing action 'CREATE_COMMENT' for user 'user_87' -----
2025-08-26 21:35:01,054 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_87_9eb80b7e).
2025-08-26 21:35:01,054 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_COMMENT'...
2025-08-26 21:35:07,262 - src.modules.content_module - INFO - Incremented comment count for post 'post_1'.
2025-08-26 21:35:12,239 - src.modules.agent_module - INFO - 用户 'user_87' 正在 关注 帖子 'post_1'...
2025-08-26 21:35:15,329 - src.modules.agent_module - INFO - 成功处理用户 'user_87' 关注 帖子 'post_1' 的操作。
2025-08-26 21:35:15,329 - src.modules.content_module - INFO - User 'user_87' successfully followed post 'post_1'.
2025-08-26 21:35:15,330 - src.modules.content_module - INFO - Added comment 'comment_79214e76' to post 'post_1' by user 'user_87'
2025-08-26 21:35:15,330 - src.modules.agent_module - INFO - 根据ID推断，正在为用户 'user_87' 的 'comments' 列表添加 ID 'comment_79214e76'...
2025-08-26 21:35:18,217 - src.modules.agent_module - INFO - 成功为用户 'user_87' 添加活动。
2025-08-26 21:35:18,218 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:35:18,220 - NotificationModule - INFO - Would send notification to user 'user_42': Your post 'post_1' was commented on by user 'user_87'.
2025-08-26 21:35:18,228 - Simulator - INFO - Added READ_POST event for user user_42 to queue. Queue size: 1
2025-08-26 21:35:18,228 - Simulator - INFO - Generated READ_POST event for user user_42 based on notification.
2025-08-26 21:35:18,229 - Engine - INFO - Comment notification sent to post author.
2025-08-26 21:35:18,234 - Engine - INFO - Sent new comment notification to 1 post followers.
2025-08-26 21:35:18,234 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_87'...
2025-08-26 21:35:18,234 - src.modules.belief_module - INFO - 开始处理用户 'user_87' 的认知流程...
2025-08-26 21:35:18,249 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:35:18,249 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:35:18,249 - src.modules.belief_module - INFO - 用户 'user_87' 的短期记忆中未找到显著议题簇
2025-08-26 21:35:18,249 - Engine - INFO - Cognitive processing for user 'user_87' finished.
2025-08-26 21:35:18,249 - Engine - INFO - ----- Action 'CREATE_COMMENT' for user 'user_87' processed successfully. -----
2025-08-26 21:35:18,250 - Simulator - INFO - Processing event from queue: READ_POST for user user_42
2025-08-26 21:35:18,250 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_42' -----
2025-08-26 21:35:24,457 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_42_2d9c048d).
2025-08-26 21:35:24,457 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:35:27,120 - src.modules.content_module - INFO - Incremented view count for post 'post_1'.
2025-08-26 21:35:27,120 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:35:27,120 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:35:27,120 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_42'...
2025-08-26 21:35:27,120 - src.modules.belief_module - INFO - 开始处理用户 'user_42' 的认知流程...
2025-08-26 21:35:27,135 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:35:27,135 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:35:27,135 - src.modules.belief_module - INFO - 用户 'user_42' 的短期记忆中未找到显著议题簇
2025-08-26 21:35:27,136 - Engine - INFO - Cognitive processing for user 'user_42' finished.
2025-08-26 21:35:27,136 - Engine - INFO - ----- Action 'READ_POST' for user 'user_42' processed successfully. -----
2025-08-26 21:35:27,136 - Simulator - INFO - Simulation step completed for user user_87.
2025-08-26 21:35:27,136 - Simulator - INFO - Simulation step 2/5
2025-08-26 21:35:29,497 - Simulator - INFO - Added CREATE_POST event for user user_23 to queue. Queue size: 1
2025-08-26 21:35:29,497 - Simulator - INFO - Processing event from queue: CREATE_POST for user user_23
2025-08-26 21:35:29,498 - Engine - INFO - ----- Processing action 'CREATE_POST' for user 'user_23' -----
2025-08-26 21:35:36,450 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_23_865a4721).
2025-08-26 21:35:36,450 - Engine - INFO - Step 2/4: Updating content state for action 'CREATE_POST'...
2025-08-26 21:35:38,922 - src.modules.content_module - INFO - 帖子 'post_851d5fa2' 由用户 'user_23' 创建成功。
2025-08-26 21:35:38,922 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:35:38,926 - NotificationModule - INFO - Would send notification to user 'user_84': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:38,934 - Simulator - INFO - Added READ_POST event for user user_84 to queue. Queue size: 1
2025-08-26 21:35:38,934 - Simulator - INFO - Generated READ_POST event for user user_84 based on notification.
2025-08-26 21:35:38,934 - NotificationModule - INFO - Would send notification to user 'user_96': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:38,942 - Simulator - INFO - Added READ_POST event for user user_96 to queue. Queue size: 2
2025-08-26 21:35:38,942 - Simulator - INFO - Generated READ_POST event for user user_96 based on notification.
2025-08-26 21:35:38,942 - NotificationModule - INFO - Would send notification to user 'user_44': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:38,950 - Simulator - INFO - Added READ_POST event for user user_44 to queue. Queue size: 3
2025-08-26 21:35:38,950 - Simulator - INFO - Generated READ_POST event for user user_44 based on notification.
2025-08-26 21:35:38,950 - NotificationModule - INFO - Would send notification to user 'user_80': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:38,958 - Simulator - INFO - Added READ_POST event for user user_80 to queue. Queue size: 4
2025-08-26 21:35:38,958 - Simulator - INFO - Generated READ_POST event for user user_80 based on notification.
2025-08-26 21:35:38,958 - NotificationModule - INFO - Would send notification to user 'user_78': User 'user_23' you follow has published a new post 'post_851d5fa2'.
MemoryModule: Creating memory for user 'user_12'...
MemoryModule: Successfully created memory 'mem_user_12_b5f4bc42'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_12_b5f4bc42)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_12 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_12 的认知处理...

【认知处理】开始处理用户 user_12 的认知流程
【阶段1+2】获取用户 user_12 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_12'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_12 的认知处理完成
【引擎】用户 user_12 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_12)

【模拟步骤】选择用户: user_87 执行操作 (步骤 1/5, 轮次 2/40)
【用户行为】用户 user_87 选择了 CREATE_COMMENT 操作，评论帖子 post_1："看到“守望者”AI，我只想笑。我们公司连个正常工时都算不清，天天靠PPT和汇报定生死。能干活的熬到秃..."
【事件队列】添加事件: CREATE_COMMENT (用户 user_87), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_COMMENT (用户 user_87)，评论帖子 post_1："看到“守望者”AI，我只想笑。我们公司连个正常工时都算不清，天天靠PPT和汇报定生死。能干活的熬到秃..."
【引擎】开始处理用户 user_87 的 CREATE_COMMENT 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_87'...
MemoryModule: Successfully created memory 'mem_user_87_9eb80b7e'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_87_9eb80b7e)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_87 的 CREATE_COMMENT 操作是否需要发送通知...
【通知】发送评论通知给帖子作者 user_42，评论内容："看到“守望者”AI，我只想笑。我们公司连个正常工时都算不清，天天靠PPT和汇报定生死。能干活的熬到秃..."
【通知模块】用户 user_87 评论了帖子，发送通知给帖子作者 user_42
【通知事件】生成通知事件: 用户 user_42 接收 READ_POST 通知
【通知事件】为用户 user_42 生成浏览事件，浏览帖子 post_1："惊天内幕！某顶级科技公司秘密开发名为“守望者”的超级AI，能像“天网”一样分析公司所有数据，从财务到..."
【事件队列】添加事件: READ_POST (用户 user_42), 队列大小: 1
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_87 的认知处理...

【认知处理】开始处理用户 user_87 的认知流程
【阶段1+2】获取用户 user_87 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_87'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_87 的认知处理完成
【引擎】用户 user_87 的 CREATE_COMMENT 操作处理成功
【处理成功】事件处理成功: CREATE_COMMENT (用户 user_87)
【处理事件】从队列中处理事件: READ_POST (用户 user_42)，浏览帖子 post_1："评论内容: 看到“守望者”AI，我只想笑。我们公司连个正常工时都算不清，天天靠PPT和汇报定生死。能..."
【引擎】开始处理用户 user_42 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_42'...
MemoryModule: Successfully created memory 'mem_user_42_2d9c048d'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_42_2d9c048d)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_42 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_42 的认知处理...

【认知处理】开始处理用户 user_42 的认知流程
【阶段1+2】获取用户 user_42 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_42'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_42 的认知处理完成
【引擎】用户 user_42 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_42)

【模拟步骤】选择用户: user_23 执行操作 (步骤 2/5, 轮次 2/40)
【用户行为】用户 user_23 选择了 CREATE_POST 操作，内容："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: CREATE_POST (用户 user_23), 队列大小: 1
【处理事件】从队列中处理事件: CREATE_POST (用户 user_23)，发布内容："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【引擎】开始处理用户 user_23 的 CREATE_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_23'...
MemoryModule: Successfully created memory 'mem_user_23_865a4721'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_23_865a4721)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_23 的 CREATE_POST 操作是否需要发送通知...
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_84
【通知事件】生成通知事件: 用户 user_84 接收 READ_POST 通知
【通知事件】为用户 user_84 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_84), 队列大小: 1
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_96
【通知事件】生成通知事件: 用户 user_96 接收 READ_POST 通知
【通知事件】为用户 user_96 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_96), 队列大小: 2
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_44
【通知事件】生成通知事件: 用户 user_44 接收 READ_POST 通知
【通知事件】为用户 user_44 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_44), 队列大小: 3
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_80
【通知事件】生成通知事件: 用户 user_80 接收 READ_POST 通知
【通知事件】为用户 user_80 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_80), 队列大小: 4
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_78
【通知事件】生成通知事件: 用户 user_78 接收 READ_POST 通知
【通知事件】为用户 user_78 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
2025-08-26 21:35:38,966 - Simulator - INFO - Added READ_POST event for user user_78 to queue. Queue size: 5
2025-08-26 21:35:38,966 - Simulator - INFO - Generated READ_POST event for user user_78 based on notification.
2025-08-26 21:35:38,966 - NotificationModule - INFO - Would send notification to user 'user_90': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:38,975 - Simulator - INFO - Added READ_POST event for user user_90 to queue. Queue size: 6
2025-08-26 21:35:38,975 - Simulator - INFO - Generated READ_POST event for user user_90 based on notification.
2025-08-26 21:35:38,976 - NotificationModule - INFO - Would send notification to user 'user_65': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:38,983 - Simulator - INFO - Added READ_POST event for user user_65 to queue. Queue size: 7
2025-08-26 21:35:38,983 - Simulator - INFO - Generated READ_POST event for user user_65 based on notification.
2025-08-26 21:35:38,984 - NotificationModule - INFO - Would send notification to user 'user_64': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:38,992 - Simulator - INFO - Added READ_POST event for user user_64 to queue. Queue size: 8
2025-08-26 21:35:38,992 - Simulator - INFO - Generated READ_POST event for user user_64 based on notification.
2025-08-26 21:35:38,992 - NotificationModule - INFO - Would send notification to user 'user_68': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,000 - Simulator - INFO - Added READ_POST event for user user_68 to queue. Queue size: 9
2025-08-26 21:35:39,000 - Simulator - INFO - Generated READ_POST event for user user_68 based on notification.
2025-08-26 21:35:39,000 - NotificationModule - INFO - Would send notification to user 'user_71': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,008 - Simulator - INFO - Added READ_POST event for user user_71 to queue. Queue size: 10
2025-08-26 21:35:39,008 - Simulator - INFO - Generated READ_POST event for user user_71 based on notification.
2025-08-26 21:35:39,008 - NotificationModule - INFO - Would send notification to user 'user_100': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,016 - Simulator - INFO - Added READ_POST event for user user_100 to queue. Queue size: 11
2025-08-26 21:35:39,016 - Simulator - INFO - Generated READ_POST event for user user_100 based on notification.
2025-08-26 21:35:39,016 - NotificationModule - INFO - Would send notification to user 'user_52': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,024 - Simulator - INFO - Added READ_POST event for user user_52 to queue. Queue size: 12
2025-08-26 21:35:39,024 - Simulator - INFO - Generated READ_POST event for user user_52 based on notification.
2025-08-26 21:35:39,024 - NotificationModule - INFO - Would send notification to user 'user_37': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,032 - Simulator - INFO - Added READ_POST event for user user_37 to queue. Queue size: 13
2025-08-26 21:35:39,032 - Simulator - INFO - Generated READ_POST event for user user_37 based on notification.
2025-08-26 21:35:39,032 - NotificationModule - INFO - Would send notification to user 'user_36': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,040 - Simulator - INFO - Added READ_POST event for user user_36 to queue. Queue size: 14
2025-08-26 21:35:39,040 - Simulator - INFO - Generated READ_POST event for user user_36 based on notification.
2025-08-26 21:35:39,040 - NotificationModule - INFO - Would send notification to user 'user_97': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,047 - Simulator - INFO - Added READ_POST event for user user_97 to queue. Queue size: 15
2025-08-26 21:35:39,048 - Simulator - INFO - Generated READ_POST event for user user_97 based on notification.
2025-08-26 21:35:39,048 - NotificationModule - INFO - Would send notification to user 'user_62': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,055 - Simulator - INFO - Added READ_POST event for user user_62 to queue. Queue size: 16
2025-08-26 21:35:39,056 - Simulator - INFO - Generated READ_POST event for user user_62 based on notification.
2025-08-26 21:35:39,056 - NotificationModule - INFO - Would send notification to user 'user_45': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,063 - Simulator - INFO - Added READ_POST event for user user_45 to queue. Queue size: 17
2025-08-26 21:35:39,063 - Simulator - INFO - Generated READ_POST event for user user_45 based on notification.
2025-08-26 21:35:39,064 - NotificationModule - INFO - Would send notification to user 'user_21': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,071 - Simulator - INFO - Added READ_POST event for user user_21 to queue. Queue size: 18
2025-08-26 21:35:39,071 - Simulator - INFO - Generated READ_POST event for user user_21 based on notification.
2025-08-26 21:35:39,072 - NotificationModule - INFO - Would send notification to user 'user_51': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,079 - Simulator - INFO - Added READ_POST event for user user_51 to queue. Queue size: 19
2025-08-26 21:35:39,079 - Simulator - INFO - Generated READ_POST event for user user_51 based on notification.
2025-08-26 21:35:39,079 - NotificationModule - INFO - Would send notification to user 'user_46': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,087 - Simulator - INFO - Added READ_POST event for user user_46 to queue. Queue size: 20
2025-08-26 21:35:39,087 - Simulator - INFO - Generated READ_POST event for user user_46 based on notification.
2025-08-26 21:35:39,087 - NotificationModule - INFO - Would send notification to user 'user_94': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,095 - Simulator - INFO - Added READ_POST event for user user_94 to queue. Queue size: 21
2025-08-26 21:35:39,095 - Simulator - INFO - Generated READ_POST event for user user_94 based on notification.
2025-08-26 21:35:39,095 - NotificationModule - INFO - Would send notification to user 'user_43': User 'user_23' you follow has published a new post 'post_851d5fa2'.
【事件队列】添加事件: READ_POST (用户 user_78), 队列大小: 5
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_90
【通知事件】生成通知事件: 用户 user_90 接收 READ_POST 通知
【通知事件】为用户 user_90 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_90), 队列大小: 6
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_65
【通知事件】生成通知事件: 用户 user_65 接收 READ_POST 通知
【通知事件】为用户 user_65 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_65), 队列大小: 7
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_64
【通知事件】生成通知事件: 用户 user_64 接收 READ_POST 通知
【通知事件】为用户 user_64 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_64), 队列大小: 8
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_68
【通知事件】生成通知事件: 用户 user_68 接收 READ_POST 通知
【通知事件】为用户 user_68 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_68), 队列大小: 9
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_71
【通知事件】生成通知事件: 用户 user_71 接收 READ_POST 通知
【通知事件】为用户 user_71 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_71), 队列大小: 10
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_100
【通知事件】生成通知事件: 用户 user_100 接收 READ_POST 通知
【通知事件】为用户 user_100 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_100), 队列大小: 11
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_52
【通知事件】生成通知事件: 用户 user_52 接收 READ_POST 通知
【通知事件】为用户 user_52 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_52), 队列大小: 12
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_37
【通知事件】生成通知事件: 用户 user_37 接收 READ_POST 通知
【通知事件】为用户 user_37 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_37), 队列大小: 13
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_36
【通知事件】生成通知事件: 用户 user_36 接收 READ_POST 通知
【通知事件】为用户 user_36 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_36), 队列大小: 14
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_97
【通知事件】生成通知事件: 用户 user_97 接收 READ_POST 通知
【通知事件】为用户 user_97 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_97), 队列大小: 15
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_62
【通知事件】生成通知事件: 用户 user_62 接收 READ_POST 通知
【通知事件】为用户 user_62 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_62), 队列大小: 16
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_45
【通知事件】生成通知事件: 用户 user_45 接收 READ_POST 通知
【通知事件】为用户 user_45 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_45), 队列大小: 17
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_21
【通知事件】生成通知事件: 用户 user_21 接收 READ_POST 通知
【通知事件】为用户 user_21 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_21), 队列大小: 18
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_51
【通知事件】生成通知事件: 用户 user_51 接收 READ_POST 通知
【通知事件】为用户 user_51 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_51), 队列大小: 19
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_46
【通知事件】生成通知事件: 用户 user_46 接收 READ_POST 通知
【通知事件】为用户 user_46 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_46), 队列大小: 20
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_94
【通知事件】生成通知事件: 用户 user_94 接收 READ_POST 通知
【通知事件】为用户 user_94 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_94), 队列大小: 21
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_43
【通知事件】生成通知事件: 用户 user_43 接收 READ_POST 通知
2025-08-26 21:35:39,103 - Simulator - INFO - Added READ_POST event for user user_43 to queue. Queue size: 22
2025-08-26 21:35:39,103 - Simulator - INFO - Generated READ_POST event for user user_43 based on notification.
2025-08-26 21:35:39,103 - NotificationModule - INFO - Would send notification to user 'user_69': User 'user_23' you follow has published a new post 'post_851d5fa2'.
2025-08-26 21:35:39,111 - Simulator - INFO - Added READ_POST event for user user_69 to queue. Queue size: 23
2025-08-26 21:35:39,111 - Simulator - INFO - Generated READ_POST event for user user_69 based on notification.
2025-08-26 21:35:39,111 - Engine - INFO - Sent new post notification to 23 followers.
2025-08-26 21:35:39,111 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_23'...
2025-08-26 21:35:39,112 - src.modules.belief_module - INFO - 开始处理用户 'user_23' 的认知流程...
2025-08-26 21:35:39,126 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:35:39,126 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:35:39,126 - src.modules.belief_module - INFO - 用户 'user_23' 的短期记忆中未找到显著议题簇
2025-08-26 21:35:39,127 - Engine - INFO - Cognitive processing for user 'user_23' finished.
2025-08-26 21:35:39,127 - Engine - INFO - ----- Action 'CREATE_POST' for user 'user_23' processed successfully. -----
2025-08-26 21:35:39,127 - Simulator - INFO - Processing event from queue: READ_POST for user user_84
2025-08-26 21:35:39,127 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_84' -----
2025-08-26 21:35:46,312 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_84_acd584f0).
2025-08-26 21:35:46,312 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:35:49,270 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:35:49,270 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:35:49,270 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:35:49,271 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_84'...
2025-08-26 21:35:49,271 - src.modules.belief_module - INFO - 开始处理用户 'user_84' 的认知流程...
2025-08-26 21:35:49,286 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:35:49,286 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:35:49,286 - src.modules.belief_module - INFO - 用户 'user_84' 的短期记忆中未找到显著议题簇
2025-08-26 21:35:49,286 - Engine - INFO - Cognitive processing for user 'user_84' finished.
2025-08-26 21:35:49,286 - Engine - INFO - ----- Action 'READ_POST' for user 'user_84' processed successfully. -----
2025-08-26 21:35:49,287 - Simulator - INFO - Processing event from queue: READ_POST for user user_96
2025-08-26 21:35:49,287 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_96' -----
2025-08-26 21:35:56,359 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_96_e59b349a).
2025-08-26 21:35:56,359 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:35:59,239 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:35:59,240 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:35:59,240 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:35:59,240 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_96'...
2025-08-26 21:35:59,240 - src.modules.belief_module - INFO - 开始处理用户 'user_96' 的认知流程...
2025-08-26 21:35:59,265 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:35:59,265 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:35:59,271 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:35:59,271 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_a62acd12' 更新或创建信念...
2025-08-26 21:35:59,329 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 0), 最大相似度: 0.3010
2025-08-26 21:35:59,329 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756214522138006'
2025-08-26 21:35:59,329 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近看到不少人讨论婚姻和继承，网上吵得挺热闹，但有些说法情绪化，容易误导人。其实很多问题都是因为信息不对称。比如前阵子某地的继承纠纷，网上闹得沸沸扬扬，最后还是靠官方声明才把事情说清楚。这才发现，官方声明不是“官腔”，而是关键时刻澄清事实、稳住局面的重要方式。它可能不最快，但最靠谱，有责任、经得起核实。尤其在涉及家庭、法律和感情的事上，更得靠权威发声，别让谣言跟着流量跑。与其瞎猜，不如多看看官方消息。'
2025-08-26 21:35:59,329 - src.modules.belief_module - INFO - embedding相似度 (0.3010) 低于阈值 (0.9)，创建节点
2025-08-26 21:35:59,330 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:35:59,330 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_a62acd12' 的证据（多线程模式）...
2025-08-26 21:36:04,852 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.4250, 反对权重: 0.0000
2025-08-26 21:36:04,852 - src.modules.belief_module - INFO - 正在为用户 'user_96' 创建新信念: '最近看到不少人讨论婚姻和继承，网上吵得挺热闹，但有些说法情绪...'
2025-08-26 21:36:08,311 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:36:08,311 - src.modules.belief_module - INFO - 正在为信念 'belief_user_96_dfaa25aa' 建立关系网络（多线程模式）...
2025-08-26 21:36:08,326 - src.modules.belief_module - INFO - 找到 3 个现有信念，开始多线程处理...
2025-08-26 21:36:19,397 - src.modules.belief_module - INFO - 建立关系 'belief_user_96_dfaa25aa' -支持/imply-> 'belief_1756214522138005' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:36:22,826 - src.modules.belief_module - INFO - 建立关系 'belief_1756214522138005' -支持/is_evidence_for-> 'belief_user_96_dfaa25aa' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:36:22,826 - src.modules.belief_module - INFO - 建立关系 'belief_user_96_dfaa25aa' -反驳/is_counter_evidence_to-> 'belief_1756214522138007' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:36:29,250 - src.modules.belief_module - INFO - 建立关系 'belief_1756214522138007' -支持/is_example_of-> 'belief_user_96_dfaa25aa' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:36:32,491 - src.modules.belief_module - INFO - 为信念 'belief_user_96_dfaa25aa' 建立了 2 个关系（多线程模式）
2025-08-26 21:36:32,492 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_96_dfaa25aa':
2025-08-26 21:36:32,492 - src.modules.belief_module - INFO -   - 真实度: 0.4250
2025-08-26 21:36:32,492 - src.modules.belief_module - INFO -   - 置信度: 0.2024
2025-08-26 21:36:32,495 - src.modules.belief_module - INFO - 认知冲击 (0.4250) 超过怀疑阈值 (0.3000)
2025-08-26 21:36:32,496 - src.modules.agent_module - INFO - 正在为用户 'user_96' 演化特质，认知冲击为: 0.42504061424717715, 内容情感强度: 0.44375
2025-08-26 21:36:32,498 - src.modules.agent_module - INFO - 基于内容情感强度 0.444 调整情绪波动性变化: -0.0038
2025-08-26 21:36:35,350 - src.modules.agent_module - INFO - 用户 'user_96' 的特质已更新:
2025-08-26 21:36:35,350 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.3425
2025-08-26 21:36:35,350 - src.modules.agent_module - INFO -   - 验证阈值: 0.2213
2025-08-26 21:36:35,350 - src.modules.agent_module - INFO -   - 情绪波动性: 0.4962
2025-08-26 21:36:35,350 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_96_dfaa25aa' 的变化，认知冲击为 0.42504061424717715...
2025-08-26 21:36:35,350 - src.modules.belief_module - INFO - 信念 'belief_user_96_dfaa25aa' 没有关联边，无需传播
2025-08-26 21:36:35,350 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.4250, 情感强度: 0.444
2025-08-26 21:36:35,351 - src.modules.belief_module - INFO - 将来自簇 cluster_a62acd12 的 2 条记忆移入历史...
2025-08-26 21:36:47,050 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:36:47,052 - Engine - INFO - Cognitive processing for user 'user_96' finished.
2025-08-26 21:36:47,052 - Engine - INFO - ----- Action 'READ_POST' for user 'user_96' processed successfully. -----
2025-08-26 21:36:47,052 - Simulator - INFO - Processing event from queue: READ_POST for user user_44
2025-08-26 21:36:47,052 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_44' -----
2025-08-26 21:36:54,273 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_44_edd2f474).
2025-08-26 21:36:54,273 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:36:57,319 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:36:57,319 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:36:57,319 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:36:57,319 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_44'...
2025-08-26 21:36:57,319 - src.modules.belief_module - INFO - 开始处理用户 'user_44' 的认知流程...
2025-08-26 21:36:57,334 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:36:57,334 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:36:57,334 - src.modules.belief_module - INFO - 用户 'user_44' 的短期记忆中未找到显著议题簇
2025-08-26 21:36:57,335 - Engine - INFO - Cognitive processing for user 'user_44' finished.
2025-08-26 21:36:57,335 - Engine - INFO - ----- Action 'READ_POST' for user 'user_44' processed successfully. -----
2025-08-26 21:36:57,335 - Simulator - INFO - Processing event from queue: READ_POST for user user_80
2025-08-26 21:36:57,335 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_80' -----
2025-08-26 21:37:04,447 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_80_7e1da6de).
2025-08-26 21:37:04,448 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:37:09,566 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:37:09,566 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:37:09,566 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:37:09,567 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_80'...
2025-08-26 21:37:09,567 - src.modules.belief_module - INFO - 开始处理用户 'user_80' 的认知流程...
2025-08-26 21:37:09,582 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:37:09,582 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:37:09,582 - src.modules.belief_module - INFO - 用户 'user_80' 的短期记忆中未找到显著议题簇
2025-08-26 21:37:09,582 - Engine - INFO - Cognitive processing for user 'user_80' finished.
2025-08-26 21:37:09,583 - Engine - INFO - ----- Action 'READ_POST' for user 'user_80' processed successfully. -----
2025-08-26 21:37:09,583 - Simulator - INFO - Processing event from queue: READ_POST for user user_78
2025-08-26 21:37:09,583 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_78' -----
2025-08-26 21:37:16,659 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_78_4a56757a).
2025-08-26 21:37:16,659 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:37:19,305 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:37:19,305 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:37:19,305 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:37:19,305 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_78'...
2025-08-26 21:37:19,305 - src.modules.belief_module - INFO - 开始处理用户 'user_78' 的认知流程...
2025-08-26 21:37:19,320 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:37:19,320 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:37:19,320 - src.modules.belief_module - INFO - 用户 'user_78' 的短期记忆中未找到显著议题簇
2025-08-26 21:37:19,321 - Engine - INFO - Cognitive processing for user 'user_78' finished.
2025-08-26 21:37:19,321 - Engine - INFO - ----- Action 'READ_POST' for user 'user_78' processed successfully. -----
【通知事件】为用户 user_43 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_43), 队列大小: 22
【通知模块】用户 user_23 发布了新帖子，发送通知给关注者 user_69
【通知事件】生成通知事件: 用户 user_69 接收 READ_POST 通知
【通知事件】为用户 user_69 生成浏览事件，浏览帖子 post_851d5fa2："最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至带着情绪在传播，我挺担心这样下去会误导很多人。其..."
【事件队列】添加事件: READ_POST (用户 user_69), 队列大小: 23
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_23 的认知处理...

【认知处理】开始处理用户 user_23 的认知流程
【阶段1+2】获取用户 user_23 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_23'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_23 的认知处理完成
【引擎】用户 user_23 的 CREATE_POST 操作处理成功
【处理成功】事件处理成功: CREATE_POST (用户 user_23)
【处理事件】从队列中处理事件: READ_POST (用户 user_84)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_84 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_84'...
MemoryModule: Successfully created memory 'mem_user_84_acd584f0'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_84_acd584f0)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_84 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_84 的认知处理...

【认知处理】开始处理用户 user_84 的认知流程
【阶段1+2】获取用户 user_84 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_84'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_84 的认知处理完成
【引擎】用户 user_84 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_84)
【处理事件】从队列中处理事件: READ_POST (用户 user_96)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_96 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_96'...
MemoryModule: Successfully created memory 'mem_user_96_e59b349a'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_96_e59b349a)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_96 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_96 的认知处理...

【认知处理】开始处理用户 user_96 的认知流程
【阶段1+2】获取用户 user_96 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_96'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_96_dfaa25aa
【步骤 4/4】完成: 用户 user_96 的认知处理完成
【引擎】用户 user_96 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_96)
【处理事件】从队列中处理事件: READ_POST (用户 user_44)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_44 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_44'...
MemoryModule: Successfully created memory 'mem_user_44_edd2f474'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_44_edd2f474)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_44 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_44 的认知处理...

【认知处理】开始处理用户 user_44 的认知流程
【阶段1+2】获取用户 user_44 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_44'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_44 的认知处理完成
【引擎】用户 user_44 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_44)
【处理事件】从队列中处理事件: READ_POST (用户 user_80)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_80 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_80'...
MemoryModule: Successfully created memory 'mem_user_80_7e1da6de'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_80_7e1da6de)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_80 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_80 的认知处理...

【认知处理】开始处理用户 user_80 的认知流程
【阶段1+2】获取用户 user_80 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_80'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_80 的认知处理完成
【引擎】用户 user_80 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_80)
【处理事件】从队列中处理事件: READ_POST (用户 user_78)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_78 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_78'...
MemoryModule: Successfully created memory 'mem_user_78_4a56757a'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_78_4a56757a)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_78 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_78 的认知处理...

【认知处理】开始处理用户 user_78 的认知流程
【阶段1+2】获取用户 user_78 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_78'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_78 的认知处理完成
【引擎】用户 user_78 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_78)
2025-08-26 21:37:19,321 - Simulator - INFO - Processing event from queue: READ_POST for user user_90
2025-08-26 21:37:19,321 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_90' -----
2025-08-26 21:37:26,285 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_90_d5c9d1c7).
2025-08-26 21:37:26,286 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:37:29,224 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:37:29,224 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:37:29,224 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:37:29,224 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_90'...
2025-08-26 21:37:29,224 - src.modules.belief_module - INFO - 开始处理用户 'user_90' 的认知流程...
2025-08-26 21:37:29,249 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:37:29,249 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:37:29,254 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:37:29,254 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_fc323348' 更新或创建信念...
2025-08-26 21:37:29,311 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 1), 最大相似度: 0.2698
2025-08-26 21:37:29,311 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756214485509635'
2025-08-26 21:37:29,311 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近跟家人聊结婚和分家产的事，突然觉得以前那种“一家人不用避讳”的想法有点危险了。现在户口本、房产证、银行流水这些，随便拍个照发群里，万一被泄露，麻烦可大了。听说有人婚前聊天记录被拿去当财产分割的证据，真是吓一跳。不是不信任家人，而是数据一旦出去，就收不回来了。现在连感情都得靠数据安全撑着，所以劝大家别随便传重要信息，也别乱授权那些“家庭管理”APP。保护隐私，不是冷淡，是真为彼此好。'
2025-08-26 21:37:29,311 - src.modules.belief_module - INFO - embedding相似度 (0.2698) 低于阈值 (0.9)，创建节点
2025-08-26 21:37:29,311 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:37:29,311 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_fc323348' 的证据（多线程模式）...
2025-08-26 21:37:35,092 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.6575, 反对权重: 0.0000
2025-08-26 21:37:35,092 - src.modules.belief_module - INFO - 正在为用户 'user_90' 创建新信念: '最近跟家人聊结婚和分家产的事，突然觉得以前那种“一家人不用避...'
2025-08-26 21:37:38,451 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:37:38,452 - src.modules.belief_module - INFO - 正在为信念 'belief_user_90_eadf9145' 建立关系网络（多线程模式）...
2025-08-26 21:37:38,465 - src.modules.belief_module - INFO - 找到 3 个现有信念，开始多线程处理...
2025-08-26 21:37:53,382 - src.modules.belief_module - INFO - 建立关系 'belief_1756214485509633' -支持/is_example_of-> 'belief_user_90_eadf9145' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:37:53,383 - src.modules.belief_module - INFO - 信念 'belief_user_90_eadf9145' 没有与其他信念建立关系（多线程模式）
2025-08-26 21:37:53,384 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_90_eadf9145':
2025-08-26 21:37:53,384 - src.modules.belief_module - INFO -   - 真实度: 0.6575
2025-08-26 21:37:53,384 - src.modules.belief_module - INFO -   - 置信度: 0.3132
2025-08-26 21:37:53,387 - src.modules.belief_module - INFO - 认知冲击 (0.6575) 超过怀疑阈值 (0.3200)
2025-08-26 21:37:53,388 - src.modules.agent_module - INFO - 正在为用户 'user_90' 演化特质，认知冲击为: 0.6575411068612773, 内容情感强度: 0.46874999999999994
2025-08-26 21:37:53,390 - src.modules.agent_module - INFO - 基于内容情感强度 0.469 调整情绪波动性变化: -0.0033
2025-08-26 21:37:56,377 - src.modules.agent_module - INFO - 用户 'user_90' 的特质已更新:
2025-08-26 21:37:56,377 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.3858
2025-08-26 21:37:56,377 - src.modules.agent_module - INFO -   - 验证阈值: 0.2329
2025-08-26 21:37:56,377 - src.modules.agent_module - INFO -   - 情绪波动性: 0.4067
2025-08-26 21:37:56,378 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_90_eadf9145' 的变化，认知冲击为 0.6575411068612773...
2025-08-26 21:37:56,378 - src.modules.belief_module - INFO - 信念 'belief_user_90_eadf9145' 没有关联边，无需传播
2025-08-26 21:37:56,378 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.6575, 情感强度: 0.469
2025-08-26 21:37:56,378 - src.modules.belief_module - INFO - 将来自簇 cluster_fc323348 的 2 条记忆移入历史...
2025-08-26 21:38:07,924 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:38:07,925 - Engine - INFO - Cognitive processing for user 'user_90' finished.
2025-08-26 21:38:07,925 - Engine - INFO - ----- Action 'READ_POST' for user 'user_90' processed successfully. -----
2025-08-26 21:38:07,926 - Simulator - INFO - Processing event from queue: READ_POST for user user_65
2025-08-26 21:38:07,926 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_65' -----
2025-08-26 21:38:15,080 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_65_5132b9d8).
2025-08-26 21:38:15,080 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:38:18,152 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:38:18,152 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:38:18,152 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:38:18,152 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_65'...
2025-08-26 21:38:18,153 - src.modules.belief_module - INFO - 开始处理用户 'user_65' 的认知流程...
2025-08-26 21:38:18,167 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:38:18,168 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:38:18,168 - src.modules.belief_module - INFO - 用户 'user_65' 的短期记忆中未找到显著议题簇
2025-08-26 21:38:18,168 - Engine - INFO - Cognitive processing for user 'user_65' finished.
2025-08-26 21:38:18,168 - Engine - INFO - ----- Action 'READ_POST' for user 'user_65' processed successfully. -----
2025-08-26 21:38:18,169 - Simulator - INFO - Processing event from queue: READ_POST for user user_64
2025-08-26 21:38:18,169 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_64' -----
2025-08-26 21:38:25,944 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_64_705834e1).
2025-08-26 21:38:25,944 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:38:28,808 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:38:28,808 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:38:28,808 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:38:28,808 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_64'...
2025-08-26 21:38:28,808 - src.modules.belief_module - INFO - 开始处理用户 'user_64' 的认知流程...
2025-08-26 21:38:28,823 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:38:28,823 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:38:28,823 - src.modules.belief_module - INFO - 用户 'user_64' 的短期记忆中未找到显著议题簇
2025-08-26 21:38:28,824 - Engine - INFO - Cognitive processing for user 'user_64' finished.
2025-08-26 21:38:28,824 - Engine - INFO - ----- Action 'READ_POST' for user 'user_64' processed successfully. -----
2025-08-26 21:38:28,824 - Simulator - INFO - Processing event from queue: READ_POST for user user_68
2025-08-26 21:38:28,824 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_68' -----
2025-08-26 21:38:36,282 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_68_1cc559ca).
2025-08-26 21:38:36,282 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:38:38,928 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:38:38,928 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:38:38,928 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:38:38,928 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_68'...
2025-08-26 21:38:38,928 - src.modules.belief_module - INFO - 开始处理用户 'user_68' 的认知流程...
2025-08-26 21:38:38,943 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:38:38,943 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:38:38,943 - src.modules.belief_module - INFO - 用户 'user_68' 的短期记忆中未找到显著议题簇
2025-08-26 21:38:38,944 - Engine - INFO - Cognitive processing for user 'user_68' finished.
2025-08-26 21:38:38,944 - Engine - INFO - ----- Action 'READ_POST' for user 'user_68' processed successfully. -----
2025-08-26 21:38:38,944 - Simulator - INFO - Processing event from queue: READ_POST for user user_71
2025-08-26 21:38:38,945 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_71' -----
2025-08-26 21:38:46,567 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_71_aa686486).
2025-08-26 21:38:46,567 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:38:49,421 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:38:49,422 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:38:49,422 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:38:49,422 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_71'...
2025-08-26 21:38:49,422 - src.modules.belief_module - INFO - 开始处理用户 'user_71' 的认知流程...
2025-08-26 21:38:49,447 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:38:49,447 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:38:49,452 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:38:49,452 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_3851ab88' 更新或创建信念...
2025-08-26 21:38:49,512 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 1), 最大相似度: 0.3303
2025-08-26 21:38:49,512 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756214334618032'
2025-08-26 21:38:49,512 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近朋友圈都在聊结婚要公证、立遗嘱，我看了直摇头。谁结婚前不签个协议？但谁又能真管得住对方私下怎么想、怎么干？我连工作电脑都像被公司监控一样，啥痕迹都藏不住。这种情况下，还指望婚姻里有秘密？别傻了。感情靠的是信任，不是合同。可现实是，嘴上说信你，转头就查你手机、翻你聊天记录。与其花大钱搞继承协议，不如先问问自己：愿不愿意把真心交给对方。毕竟，法律能管财产，管不了人心。'
2025-08-26 21:38:49,512 - src.modules.belief_module - INFO - embedding相似度 (0.3303) 低于阈值 (0.9)，创建节点
2025-08-26 21:38:49,512 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:38:49,512 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_3851ab88' 的证据（多线程模式）...
2025-08-26 21:38:55,852 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.3704, 反对权重: 0.2561
2025-08-26 21:38:55,852 - src.modules.belief_module - INFO - 正在为用户 'user_71' 创建新信念: '最近朋友圈都在聊结婚要公证、立遗嘱，我看了直摇头。谁结婚前不...'
2025-08-26 21:38:59,314 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:38:59,314 - src.modules.belief_module - INFO - 正在为信念 'belief_user_71_795455af' 建立关系网络（多线程模式）...
2025-08-26 21:38:59,330 - src.modules.belief_module - INFO - 找到 4 个现有信念，开始多线程处理...
2025-08-26 21:39:09,429 - src.modules.belief_module - INFO - 建立关系 'belief_user_71_795455af' -支持/imply-> 'belief_1756214334618032' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:39:12,791 - src.modules.belief_module - INFO - 建立关系 'belief_1756214334618032' -支持/imply-> 'belief_user_71_795455af' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:39:16,066 - src.modules.belief_module - INFO - 建立关系 'belief_1756214334618033' -支持/imply-> 'belief_user_71_795455af' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:39:19,473 - src.modules.belief_module - INFO - 建立关系 'belief_1756214334618035' -反驳/diminishes-> 'belief_user_71_795455af' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:39:22,982 - src.modules.belief_module - INFO - 为信念 'belief_user_71_795455af' 建立了 1 个关系（多线程模式）
2025-08-26 21:39:22,982 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_71_795455af':
2025-08-26 21:39:22,983 - src.modules.belief_module - INFO -   - 真实度: 0.1143
2025-08-26 21:39:22,983 - src.modules.belief_module - INFO -   - 置信度: 0.1000
2025-08-26 21:39:22,986 - src.modules.belief_module - INFO - 信念 'belief_user_71_795455af' 的置信度 (0.1000) 低于求证阈值 (0.2500)
2025-08-26 21:39:22,986 - src.modules.belief_module - INFO - 正在为用户 'user_71' 触发对信念 'belief_user_71_795455af' 的求证机制...
2025-08-26 21:39:22,987 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境
2025-08-26 21:39:22,987 - src.modules.belief_module - INFO - 搜索结果:
2025-08-26 21:39:22,987 - src.modules.belief_module - INFO - 1. 这个谣言虽然是假的，但它能火爆全网，恰恰说明了一个现实问题：我国对非婚同居关系的法律保护确实存在空白。现实中，一方为家庭付出多年，但因没有一纸婚书，分手或一方离世后权益无法保障的案例比比皆是。我们不应止于辟谣，更应思考，如何在保护私有财产权和维护传统婚姻制度之间，为“事实伴侣”提供一条人道的、有限的救济途径？比如在分割同居期间共同财产（析产）或请求扶养补偿方面，是否可以有更明确的规定？#法律思考 #非婚同居 #民法典
2025-08-26 21:39:22,987 - src.modules.belief_module - INFO - 2. 大家别慌，我们国家的法律基石是很稳固的。关于财产，尤其是房子这种大事，核心就一条：不动产登记簿上写谁，就是谁的。这叫物权公示公信原则。别说同居一年，就是同居十年，只要房本没你名，法律上你就没份。所有让你焦虑的说法，都绕不开这个基本原则。所以，守好你的产权证，比什么都强。#物权法 #不动产登记 #硬核知识
2025-08-26 21:39:22,987 - src.modules.belief_module - INFO - 生成了求证搜索行为: '理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境'
2025-08-26 21:39:22,987 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-26 21:39:22,987 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-26 21:39:22,988 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-26 21:39:22,988 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '理性讨论：从“同居继承”谣言看我国非婚同居关系的法律困境'
2025-08-26 21:39:22,988 - src.modules.belief_module - INFO - 将来自簇 cluster_3851ab88 的 2 条记忆移入历史...
2025-08-26 21:39:33,962 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:39:33,963 - Engine - INFO - Cognitive processing for user 'user_71' finished.
2025-08-26 21:39:33,964 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-26 21:39:33,964 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-26 21:39:33,964 - Engine - INFO - Processing queued verification action for user 'user_71'
2025-08-26 21:39:33,964 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_71' -----
2025-08-26 21:39:41,050 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_71_3be1aa43).
2025-08-26 21:39:41,050 - Engine - INFO - No target_id provided for a content update action that requires one.
2025-08-26 21:39:41,050 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:39:41,050 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:39:41,050 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_71'...
2025-08-26 21:39:41,051 - src.modules.belief_module - INFO - 开始处理用户 'user_71' 的认知流程...
2025-08-26 21:39:41,065 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:39:41,066 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:39:41,066 - src.modules.belief_module - INFO - 用户 'user_71' 的短期记忆中未找到显著议题簇
2025-08-26 21:39:41,066 - Engine - INFO - Cognitive processing for user 'user_71' finished.
2025-08-26 21:39:41,066 - Engine - INFO - ----- Action 'READ_POST' for user 'user_71' processed successfully. -----
2025-08-26 21:39:41,067 - Engine - INFO - Queued verification action processed successfully
2025-08-26 21:39:41,067 - Engine - INFO - All verification actions processed.
2025-08-26 21:39:41,067 - Engine - INFO - Verification queue processing finished.
2025-08-26 21:39:41,067 - Engine - INFO - ----- Action 'READ_POST' for user 'user_71' processed successfully. -----
【处理事件】从队列中处理事件: READ_POST (用户 user_90)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_90 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_90'...
MemoryModule: Successfully created memory 'mem_user_90_d5c9d1c7'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_90_d5c9d1c7)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_90 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_90 的认知处理...

【认知处理】开始处理用户 user_90 的认知流程
【阶段1+2】获取用户 user_90 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_90'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_90_eadf9145
【步骤 4/4】完成: 用户 user_90 的认知处理完成
【引擎】用户 user_90 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_90)
【处理事件】从队列中处理事件: READ_POST (用户 user_65)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_65 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_65'...
MemoryModule: Successfully created memory 'mem_user_65_5132b9d8'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_65_5132b9d8)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_65 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_65 的认知处理...

【认知处理】开始处理用户 user_65 的认知流程
【阶段1+2】获取用户 user_65 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_65'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_65 的认知处理完成
【引擎】用户 user_65 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_65)
【处理事件】从队列中处理事件: READ_POST (用户 user_64)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_64 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_64'...
MemoryModule: Successfully created memory 'mem_user_64_705834e1'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_64_705834e1)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_64 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_64 的认知处理...

【认知处理】开始处理用户 user_64 的认知流程
【阶段1+2】获取用户 user_64 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_64'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_64 的认知处理完成
【引擎】用户 user_64 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_64)
【处理事件】从队列中处理事件: READ_POST (用户 user_68)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_68 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_68'...
MemoryModule: Successfully created memory 'mem_user_68_1cc559ca'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_68_1cc559ca)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_68 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_68 的认知处理...

【认知处理】开始处理用户 user_68 的认知流程
【阶段1+2】获取用户 user_68 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_68'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_68 的认知处理完成
【引擎】用户 user_68 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_68)
【处理事件】从队列中处理事件: READ_POST (用户 user_71)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_71 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_71'...
MemoryModule: Successfully created memory 'mem_user_71_aa686486'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_71_aa686486)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_71 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_71 的认知处理...

【认知处理】开始处理用户 user_71 的认知流程
【阶段1+2】获取用户 user_71 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_71'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_71_795455af
【步骤 4/4】完成: 用户 user_71 的认知处理完成
【步骤 5/5】处理求证队列，队列大小: 1
【引擎】开始处理用户 user_71 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_71'...
MemoryModule: Successfully created memory 'mem_user_71_3be1aa43'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_71_3be1aa43)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_71 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_71 的认知处理...

【认知处理】开始处理用户 user_71 的认知流程
【阶段1+2】获取用户 user_71 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_71'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_71 的认知处理完成
【引擎】用户 user_71 的 READ_POST 操作处理成功
【步骤 5/5】完成: 求证队列处理完成
【引擎】用户 user_71 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_71)
2025-08-26 21:39:41,067 - Simulator - INFO - Processing event from queue: READ_POST for user user_100
2025-08-26 21:39:41,068 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_100' -----
2025-08-26 21:39:48,705 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_100_95669ef6).
2025-08-26 21:39:48,705 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:39:51,878 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:39:51,878 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:39:51,878 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:39:51,878 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_100'...
2025-08-26 21:39:51,878 - src.modules.belief_module - INFO - 开始处理用户 'user_100' 的认知流程...
2025-08-26 21:39:51,902 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:39:51,902 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:39:51,907 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:39:51,908 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_de57deb2' 更新或创建信念...
2025-08-26 21:39:51,965 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 1), 最大相似度: 0.4115
2025-08-26 21:39:51,965 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756214546863436'
2025-08-26 21:39:51,965 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近跟家人聊结婚和分家产的事，突然发现以前那些“一家人不用避讳”的东西，现在可能成隐患了。爸妈那辈人啥都摆在桌上说，可现在身份证、房产证、银行流水这些信息要是被泄露，后果可不止家里闹矛盾这么简单。我看到有人婚前聊天记录被拿去当财产分割的证据，吓一跳。现在结婚讲信任，但信任也得有数据安全做底子。我劝家里人，重要的财务和身份信息别随便拍照发群里，也别随便给那些“家庭管理”APP授权。不是不信任人，是数据一出去就收不回来了。保护隐私不是冷淡，反而是对家人更负责。'
2025-08-26 21:39:51,965 - src.modules.belief_module - INFO - embedding相似度 (0.4115) 低于阈值 (0.9)，创建节点
2025-08-26 21:39:51,965 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:39:51,965 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_de57deb2' 的证据（多线程模式）...
2025-08-26 21:39:59,006 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.6575, 反对权重: 0.0000
2025-08-26 21:39:59,006 - src.modules.belief_module - INFO - 正在为用户 'user_100' 创建新信念: '最近跟家人聊结婚和分家产的事，突然发现以前那些“一家人不用避...'
2025-08-26 21:40:02,535 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:40:02,536 - src.modules.belief_module - INFO - 正在为信念 'belief_user_100_0d873671' 建立关系网络（多线程模式）...
2025-08-26 21:40:02,549 - src.modules.belief_module - INFO - 找到 3 个现有信念，开始多线程处理...
2025-08-26 21:40:12,568 - src.modules.belief_module - INFO - 建立关系 'belief_user_100_0d873671' -支持/is_example_of-> 'belief_1756214546863437' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:40:15,862 - src.modules.belief_module - INFO - 建立关系 'belief_1756214546863437' -支持/is_example_of-> 'belief_user_100_0d873671' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:40:15,863 - src.modules.belief_module - INFO - 建立关系 'belief_user_100_0d873671' -反驳/diminishes-> 'belief_1756214546863436' (权重: 0.75, 置信度: 0.87)
2025-08-26 21:40:19,329 - src.modules.belief_module - INFO - 建立关系 'belief_1756214546863436' -支持/imply-> 'belief_user_100_0d873671' (权重: 0.85, 置信度: 0.92)
2025-08-26 21:40:22,804 - src.modules.belief_module - INFO - 建立关系 'belief_1756214546863435' -支持/imply-> 'belief_user_100_0d873671' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:40:26,078 - src.modules.belief_module - INFO - 为信念 'belief_user_100_0d873671' 建立了 2 个关系（多线程模式）
2025-08-26 21:40:26,078 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_100_0d873671':
2025-08-26 21:40:26,078 - src.modules.belief_module - INFO -   - 真实度: 0.6575
2025-08-26 21:40:26,079 - src.modules.belief_module - INFO -   - 置信度: 0.3132
2025-08-26 21:40:26,082 - src.modules.belief_module - INFO - 认知冲击 (0.6575) 超过怀疑阈值 (0.3200)
2025-08-26 21:40:26,082 - src.modules.agent_module - INFO - 正在为用户 'user_100' 演化特质，认知冲击为: 0.6575411068612773, 内容情感强度: 0.5249999999999999
2025-08-26 21:40:26,086 - src.modules.agent_module - INFO - 基于内容情感强度 0.525 调整情绪波动性变化: 0.0026
2025-08-26 21:40:29,168 - src.modules.agent_module - INFO - 用户 'user_100' 的特质已更新:
2025-08-26 21:40:29,168 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.3858
2025-08-26 21:40:29,169 - src.modules.agent_module - INFO -   - 验证阈值: 0.2329
2025-08-26 21:40:29,169 - src.modules.agent_module - INFO -   - 情绪波动性: 0.8026
2025-08-26 21:40:29,169 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_100_0d873671' 的变化，认知冲击为 0.6575411068612773...
2025-08-26 21:40:29,169 - src.modules.belief_module - INFO - 信念 'belief_user_100_0d873671' 没有关联边，无需传播
2025-08-26 21:40:29,169 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.6575, 情感强度: 0.525
2025-08-26 21:40:29,169 - src.modules.belief_module - INFO - 将来自簇 cluster_de57deb2 的 2 条记忆移入历史...
2025-08-26 21:40:44,326 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:40:44,328 - Engine - INFO - Cognitive processing for user 'user_100' finished.
2025-08-26 21:40:44,328 - Engine - INFO - ----- Action 'READ_POST' for user 'user_100' processed successfully. -----
2025-08-26 21:40:44,328 - Simulator - INFO - Processing event from queue: READ_POST for user user_52
2025-08-26 21:40:44,329 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_52' -----
2025-08-26 21:40:51,706 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_52_12cbc778).
2025-08-26 21:40:51,706 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:40:54,723 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:40:54,723 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:40:54,723 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:40:54,723 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_52'...
2025-08-26 21:40:54,723 - src.modules.belief_module - INFO - 开始处理用户 'user_52' 的认知流程...
2025-08-26 21:40:54,738 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:40:54,738 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:40:54,739 - src.modules.belief_module - INFO - 用户 'user_52' 的短期记忆中未找到显著议题簇
2025-08-26 21:40:54,739 - Engine - INFO - Cognitive processing for user 'user_52' finished.
2025-08-26 21:40:54,739 - Engine - INFO - ----- Action 'READ_POST' for user 'user_52' processed successfully. -----
2025-08-26 21:40:54,740 - Simulator - INFO - Processing event from queue: READ_POST for user user_37
2025-08-26 21:40:54,740 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_37' -----
2025-08-26 21:41:01,714 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_37_265e1d3d).
2025-08-26 21:41:01,714 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:41:04,578 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:41:04,578 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:41:04,579 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:41:04,579 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_37'...
2025-08-26 21:41:04,579 - src.modules.belief_module - INFO - 开始处理用户 'user_37' 的认知流程...
2025-08-26 21:41:04,594 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:41:04,594 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:41:04,594 - src.modules.belief_module - INFO - 用户 'user_37' 的短期记忆中未找到显著议题簇
2025-08-26 21:41:04,595 - Engine - INFO - Cognitive processing for user 'user_37' finished.
2025-08-26 21:41:04,595 - Engine - INFO - ----- Action 'READ_POST' for user 'user_37' processed successfully. -----
2025-08-26 21:41:04,595 - Simulator - INFO - Processing event from queue: READ_POST for user user_36
2025-08-26 21:41:04,595 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_36' -----
2025-08-26 21:41:11,717 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_36_fe24bfa4).
2025-08-26 21:41:11,717 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:41:16,875 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:41:16,876 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:41:16,876 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:41:16,876 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_36'...
2025-08-26 21:41:16,876 - src.modules.belief_module - INFO - 开始处理用户 'user_36' 的认知流程...
2025-08-26 21:41:16,890 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:41:16,890 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:41:16,890 - src.modules.belief_module - INFO - 用户 'user_36' 的短期记忆中未找到显著议题簇
2025-08-26 21:41:16,890 - Engine - INFO - Cognitive processing for user 'user_36' finished.
2025-08-26 21:41:16,890 - Engine - INFO - ----- Action 'READ_POST' for user 'user_36' processed successfully. -----
2025-08-26 21:41:16,891 - Simulator - INFO - Processing event from queue: READ_POST for user user_97
2025-08-26 21:41:16,891 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_97' -----
2025-08-26 21:41:23,920 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_97_63073ac5).
2025-08-26 21:41:23,921 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:41:26,608 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:41:26,608 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:41:26,608 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:41:26,608 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_97'...
2025-08-26 21:41:26,608 - src.modules.belief_module - INFO - 开始处理用户 'user_97' 的认知流程...
2025-08-26 21:41:26,624 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:41:26,624 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:41:26,624 - src.modules.belief_module - INFO - 用户 'user_97' 的短期记忆中未找到显著议题簇
2025-08-26 21:41:26,624 - Engine - INFO - Cognitive processing for user 'user_97' finished.
2025-08-26 21:41:26,624 - Engine - INFO - ----- Action 'READ_POST' for user 'user_97' processed successfully. -----
2025-08-26 21:41:26,625 - Simulator - INFO - Processing event from queue: READ_POST for user user_62
2025-08-26 21:41:26,625 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_62' -----
2025-08-26 21:41:33,838 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_62_a29700dd).
2025-08-26 21:41:33,838 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:41:36,491 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:41:36,491 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:41:36,491 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:41:36,491 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_62'...
2025-08-26 21:41:36,492 - src.modules.belief_module - INFO - 开始处理用户 'user_62' 的认知流程...
2025-08-26 21:41:36,516 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:41:36,517 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:41:36,521 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:41:36,522 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_286e12f8' 更新或创建信念...
2025-08-26 21:41:36,592 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 1), 最大相似度: 0.4666
2025-08-26 21:41:36,592 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756214252313580'
2025-08-26 21:41:36,593 - src.modules.belief_module - INFO - 选择的备选信念命题: '这事儿确实挺吓人，但别慌。要是真有公司偷偷用咱们的邮件训练AI，那可就违法了。反正迟早会曝光，证据一出来，大伙儿一起告，法院见！法律管得住，谁违法谁负责，咱们人多，不怕！'
2025-08-26 21:41:36,593 - src.modules.belief_module - INFO - embedding相似度 (0.4666) 低于阈值 (0.9)，创建节点
2025-08-26 21:41:36,593 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:41:36,593 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_286e12f8' 的证据（多线程模式）...
2025-08-26 21:41:41,121 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.5714, 反对权重: 0.0000
2025-08-26 21:41:41,122 - src.modules.belief_module - INFO - 正在为用户 'user_62' 创建新信念: '这事儿确实挺吓人，但别慌。要是真有公司偷偷用咱们的邮件训练A...'
2025-08-26 21:41:44,558 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:41:44,558 - src.modules.belief_module - INFO - 正在为信念 'belief_user_62_1e090f24' 建立关系网络（多线程模式）...
2025-08-26 21:41:44,575 - src.modules.belief_module - INFO - 找到 4 个现有信念，开始多线程处理...
2025-08-26 21:41:55,591 - src.modules.belief_module - INFO - 建立关系 'belief_user_62_1e090f24' -支持/imply-> 'belief_1756214252313580' (权重: 0.95, 置信度: 0.98)
2025-08-26 21:41:58,924 - src.modules.belief_module - INFO - 建立关系 'belief_1756214252313580' -支持/imply-> 'belief_user_62_1e090f24' (权重: 0.92, 置信度: 0.95)
2025-08-26 21:42:02,306 - src.modules.belief_module - INFO - 为信念 'belief_user_62_1e090f24' 建立了 1 个关系（多线程模式）
2025-08-26 21:42:02,307 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_62_1e090f24':
2025-08-26 21:42:02,307 - src.modules.belief_module - INFO -   - 真实度: 0.5714
2025-08-26 21:42:02,307 - src.modules.belief_module - INFO -   - 置信度: 0.2722
2025-08-26 21:42:02,311 - src.modules.belief_module - INFO - 信念 'belief_user_62_1e090f24' 的置信度 (0.2722) 低于求证阈值 (0.4500)
2025-08-26 21:42:02,311 - src.modules.belief_module - INFO - 正在为用户 'user_62' 触发对信念 'belief_user_62_1e090f24' 的求证机制...
2025-08-26 21:42:02,311 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！
2025-08-26 21:42:02,311 - src.modules.belief_module - INFO - 搜索结果:
2025-08-26 21:42:02,311 - src.modules.belief_module - INFO - 1. 各位朋友，关于网传的“同居一年继承一半财产”的说法是彻头彻尾的谣言。根据我国《民法典》，同居伴侣不属于法定继承人。继承顺序第一位是配偶、子女、父母。除非逝者生前立下合法有效的遗嘱，明确将财产遗赠给同居伴侣，否则伴侣一分钱也继承不到。房本写谁名就是谁的，这叫不动产登记原则。别再被那些贩卖焦虑的谣言欺骗了！#法律科普 #辟谣 #继承法
2025-08-26 21:42:02,312 - src.modules.belief_module - INFO - 2. 大家别慌，我们国家的法律基石是很稳固的。关于财产，尤其是房子这种大事，核心就一条：不动产登记簿上写谁，就是谁的。这叫物权公示公信原则。别说同居一年，就是同居十年，只要房本没你名，法律上你就没份。所有让你焦虑的说法，都绕不开这个基本原则。所以，守好你的产权证，比什么都强。#物权法 #不动产登记 #硬核知识
2025-08-26 21:42:02,312 - src.modules.belief_module - INFO - 生成了求证搜索行为: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-26 21:42:02,312 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-26 21:42:02,312 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-26 21:42:02,312 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-26 21:42:02,312 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-26 21:42:02,312 - src.modules.belief_module - INFO - 认知冲击 (0.5714) 超过怀疑阈值 (0.3100)
2025-08-26 21:42:02,312 - src.modules.agent_module - INFO - 正在为用户 'user_62' 演化特质，认知冲击为: 0.5714020314044546, 内容情感强度: 0.48124999999999996
2025-08-26 21:42:02,315 - src.modules.agent_module - INFO - 基于内容情感强度 0.481 调整情绪波动性变化: -0.0017
2025-08-26 21:42:05,241 - src.modules.agent_module - INFO - 用户 'user_62' 的特质已更新:
2025-08-26 21:42:05,241 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.3671
2025-08-26 21:42:05,241 - src.modules.agent_module - INFO -   - 验证阈值: 0.4786
2025-08-26 21:42:05,241 - src.modules.agent_module - INFO -   - 情绪波动性: 0.2883
2025-08-26 21:42:05,241 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_62_1e090f24' 的变化，认知冲击为 0.5714020314044546...
2025-08-26 21:42:05,241 - src.modules.belief_module - INFO - 信念 'belief_user_62_1e090f24' 没有关联边，无需传播
2025-08-26 21:42:05,242 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.5714, 情感强度: 0.481
2025-08-26 21:42:05,242 - src.modules.belief_module - INFO - 将来自簇 cluster_286e12f8 的 2 条记忆移入历史...
2025-08-26 21:42:17,718 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:42:17,720 - Engine - INFO - Cognitive processing for user 'user_62' finished.
2025-08-26 21:42:17,720 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-26 21:42:17,720 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-26 21:42:17,720 - Engine - INFO - Processing queued verification action for user 'user_62'
2025-08-26 21:42:17,720 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_62' -----
【处理事件】从队列中处理事件: READ_POST (用户 user_100)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_100 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_100'...
MemoryModule: Successfully created memory 'mem_user_100_95669ef6'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_100_95669ef6)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_100 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_100 的认知处理...

【认知处理】开始处理用户 user_100 的认知流程
【阶段1+2】获取用户 user_100 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_100'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_100_0d873671
【步骤 4/4】完成: 用户 user_100 的认知处理完成
【引擎】用户 user_100 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_100)
【处理事件】从队列中处理事件: READ_POST (用户 user_52)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_52 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_52'...
MemoryModule: Successfully created memory 'mem_user_52_12cbc778'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_52_12cbc778)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_52 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_52 的认知处理...

【认知处理】开始处理用户 user_52 的认知流程
【阶段1+2】获取用户 user_52 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_52'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_52 的认知处理完成
【引擎】用户 user_52 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_52)
【处理事件】从队列中处理事件: READ_POST (用户 user_37)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_37 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_37'...
MemoryModule: Successfully created memory 'mem_user_37_265e1d3d'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_37_265e1d3d)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_37 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_37 的认知处理...

【认知处理】开始处理用户 user_37 的认知流程
【阶段1+2】获取用户 user_37 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_37'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_37 的认知处理完成
【引擎】用户 user_37 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_37)
【处理事件】从队列中处理事件: READ_POST (用户 user_36)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_36 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_36'...
MemoryModule: Successfully created memory 'mem_user_36_fe24bfa4'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_36_fe24bfa4)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_36 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_36 的认知处理...

【认知处理】开始处理用户 user_36 的认知流程
【阶段1+2】获取用户 user_36 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_36'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_36 的认知处理完成
【引擎】用户 user_36 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_36)
【处理事件】从队列中处理事件: READ_POST (用户 user_97)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_97 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_97'...
MemoryModule: Successfully created memory 'mem_user_97_63073ac5'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_97_63073ac5)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_97 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_97 的认知处理...

【认知处理】开始处理用户 user_97 的认知流程
【阶段1+2】获取用户 user_97 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_97'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_97 的认知处理完成
【引擎】用户 user_97 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_97)
【处理事件】从队列中处理事件: READ_POST (用户 user_62)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_62 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_62'...
MemoryModule: Successfully created memory 'mem_user_62_a29700dd'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_62_a29700dd)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_62 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_62 的认知处理...

【认知处理】开始处理用户 user_62 的认知流程
【阶段1+2】获取用户 user_62 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_62'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_62_1e090f24
【步骤 4/4】完成: 用户 user_62 的认知处理完成
【步骤 5/5】处理求证队列，队列大小: 1
【引擎】开始处理用户 user_62 的 READ_POST 操作
2025-08-26 21:42:24,324 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_62_70e9c2f4).
2025-08-26 21:42:24,324 - Engine - INFO - No target_id provided for a content update action that requires one.
2025-08-26 21:42:24,324 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:42:24,324 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:42:24,324 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_62'...
2025-08-26 21:42:24,324 - src.modules.belief_module - INFO - 开始处理用户 'user_62' 的认知流程...
2025-08-26 21:42:24,339 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:42:24,339 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:42:24,339 - src.modules.belief_module - INFO - 用户 'user_62' 的短期记忆中未找到显著议题簇
2025-08-26 21:42:24,339 - Engine - INFO - Cognitive processing for user 'user_62' finished.
2025-08-26 21:42:24,340 - Engine - INFO - ----- Action 'READ_POST' for user 'user_62' processed successfully. -----
2025-08-26 21:42:24,340 - Engine - INFO - Queued verification action processed successfully
2025-08-26 21:42:24,340 - Engine - INFO - All verification actions processed.
2025-08-26 21:42:24,340 - Engine - INFO - Verification queue processing finished.
2025-08-26 21:42:24,340 - Engine - INFO - ----- Action 'READ_POST' for user 'user_62' processed successfully. -----
2025-08-26 21:42:24,341 - Simulator - INFO - Processing event from queue: READ_POST for user user_45
2025-08-26 21:42:24,341 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_45' -----
2025-08-26 21:42:31,445 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_45_38dccfb1).
2025-08-26 21:42:31,446 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:42:34,485 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:42:34,485 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:42:34,485 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:42:34,485 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_45'...
2025-08-26 21:42:34,486 - src.modules.belief_module - INFO - 开始处理用户 'user_45' 的认知流程...
2025-08-26 21:42:34,500 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:42:34,501 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:42:34,501 - src.modules.belief_module - INFO - 用户 'user_45' 的短期记忆中未找到显著议题簇
2025-08-26 21:42:34,501 - Engine - INFO - Cognitive processing for user 'user_45' finished.
2025-08-26 21:42:34,501 - Engine - INFO - ----- Action 'READ_POST' for user 'user_45' processed successfully. -----
2025-08-26 21:42:34,502 - Simulator - INFO - Processing event from queue: READ_POST for user user_21
2025-08-26 21:42:34,502 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_21' -----
2025-08-26 21:42:41,774 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_21_884940e9).
2025-08-26 21:42:41,775 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:42:44,770 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:42:44,771 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:42:44,771 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:42:44,771 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_21'...
2025-08-26 21:42:44,771 - src.modules.belief_module - INFO - 开始处理用户 'user_21' 的认知流程...
2025-08-26 21:42:44,796 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:42:44,796 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:42:44,801 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:42:44,801 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_f3496bb4' 更新或创建信念...
2025-08-26 21:42:44,883 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 0), 最大相似度: 0.3102
2025-08-26 21:42:44,883 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756213796301433'
2025-08-26 21:42:44,883 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近看到不少人聊婚姻和继承，网上吵得挺凶，有些说法还带情绪，挺让人担心的。其实很多问题都是因为 info 不对称。前阵子某地出个继承纠纷，网上闹得厉害，最后还是靠官方声明才把事情说清楚。这才发现，官方声明不是“官腔”，而是关键时刻澄清事实、稳住局面的重要手段。它不一定最快，但靠谱、有责任。尤其涉及婚姻、继承这种牵扯家庭、法律和感情的事，更得靠权威发声，别让谣言满天飞。与其在社交平台瞎猜，不如多看看官方消息。真相不该被流量带偏。'
2025-08-26 21:42:44,883 - src.modules.belief_module - INFO - embedding相似度 (0.3102) 低于阈值 (0.9)，创建节点
2025-08-26 21:42:44,884 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:42:44,884 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_f3496bb4' 的证据（多线程模式）...
2025-08-26 21:42:50,589 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.4250, 反对权重: 0.0000
2025-08-26 21:42:50,589 - src.modules.belief_module - INFO - 正在为用户 'user_21' 创建新信念: '最近看到不少人聊婚姻和继承，网上吵得挺凶，有些说法还带情绪，...'
2025-08-26 21:42:53,992 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:42:53,992 - src.modules.belief_module - INFO - 正在为信念 'belief_user_21_2ff77a97' 建立关系网络（多线程模式）...
2025-08-26 21:42:54,008 - src.modules.belief_module - INFO - 找到 5 个现有信念，开始多线程处理...
2025-08-26 21:43:13,710 - src.modules.belief_module - INFO - 信念 'belief_user_21_2ff77a97' 没有与其他信念建立关系（多线程模式）
2025-08-26 21:43:13,710 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_21_2ff77a97':
2025-08-26 21:43:13,710 - src.modules.belief_module - INFO -   - 真实度: 0.4250
2025-08-26 21:43:13,710 - src.modules.belief_module - INFO -   - 置信度: 0.2024
2025-08-26 21:43:13,714 - src.modules.belief_module - INFO - 信念 'belief_user_21_2ff77a97' 的置信度 (0.2024) 低于求证阈值 (0.7800)
2025-08-26 21:43:13,714 - src.modules.belief_module - INFO - 正在为用户 'user_21' 触发对信念 'belief_user_21_2ff77a97' 的求证机制...
2025-08-26 21:43:13,715 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！
2025-08-26 21:43:13,715 - src.modules.belief_module - INFO - 搜索结果:
2025-08-26 21:43:13,715 - src.modules.belief_module - INFO - 1. 各位朋友，关于网传的“同居一年继承一半财产”的说法是彻头彻尾的谣言。根据我国《民法典》，同居伴侣不属于法定继承人。继承顺序第一位是配偶、子女、父母。除非逝者生前立下合法有效的遗嘱，明确将财产遗赠给同居伴侣，否则伴侣一分钱也继承不到。房本写谁名就是谁的，这叫不动产登记原则。别再被那些贩卖焦虑的谣言欺骗了！#法律科普 #辟谣 #继承法
2025-08-26 21:43:13,715 - src.modules.belief_module - INFO - 2. 大家别慌，我们国家的法律基石是很稳固的。关于财产，尤其是房子这种大事，核心就一条：不动产登记簿上写谁，就是谁的。这叫物权公示公信原则。别说同居一年，就是同居十年，只要房本没你名，法律上你就没份。所有让你焦虑的说法，都绕不开这个基本原则。所以，守好你的产权证，比什么都强。#物权法 #不动产登记 #硬核知识
2025-08-26 21:43:13,715 - src.modules.belief_module - INFO - 生成了求证搜索行为: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-26 21:43:13,715 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-26 21:43:13,715 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-26 21:43:13,716 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-26 21:43:13,716 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '【深度辟谣】别再传“同居继承一半财产”了！律师告诉你真实情况是这样！'
2025-08-26 21:43:13,716 - src.modules.belief_module - INFO - 将来自簇 cluster_f3496bb4 的 2 条记忆移入历史...
2025-08-26 21:43:24,918 - src.modules.belief_module - INFO - 成功移动 2/2 条记忆到历史存档
2025-08-26 21:43:24,919 - Engine - INFO - Cognitive processing for user 'user_21' finished.
2025-08-26 21:43:24,920 - Engine - INFO - Step 5/5: Processing verification queue with 1 actions...
2025-08-26 21:43:24,920 - Engine - INFO - Processing 1 verification actions from queue...
2025-08-26 21:43:24,920 - Engine - INFO - Processing queued verification action for user 'user_21'
2025-08-26 21:43:24,920 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_21' -----
2025-08-26 21:43:31,398 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_21_9b8255b8).
2025-08-26 21:43:31,398 - Engine - INFO - No target_id provided for a content update action that requires one.
2025-08-26 21:43:31,398 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:43:31,398 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:43:31,398 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_21'...
2025-08-26 21:43:31,399 - src.modules.belief_module - INFO - 开始处理用户 'user_21' 的认知流程...
2025-08-26 21:43:31,414 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:43:31,414 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:43:31,414 - src.modules.belief_module - INFO - 用户 'user_21' 的短期记忆中未找到显著议题簇
2025-08-26 21:43:31,415 - Engine - INFO - Cognitive processing for user 'user_21' finished.
2025-08-26 21:43:31,415 - Engine - INFO - ----- Action 'READ_POST' for user 'user_21' processed successfully. -----
2025-08-26 21:43:31,415 - Engine - INFO - Queued verification action processed successfully
2025-08-26 21:43:31,415 - Engine - INFO - All verification actions processed.
2025-08-26 21:43:31,415 - Engine - INFO - Verification queue processing finished.
2025-08-26 21:43:31,415 - Engine - INFO - ----- Action 'READ_POST' for user 'user_21' processed successfully. -----
2025-08-26 21:43:31,416 - Simulator - INFO - Processing event from queue: READ_POST for user user_51
2025-08-26 21:43:31,416 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_51' -----
2025-08-26 21:43:38,479 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_51_449e4e6b).
2025-08-26 21:43:38,479 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:43:41,552 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:43:41,552 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:43:41,552 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:43:41,552 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_51'...
2025-08-26 21:43:41,552 - src.modules.belief_module - INFO - 开始处理用户 'user_51' 的认知流程...
2025-08-26 21:43:41,567 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:43:41,567 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:43:41,567 - src.modules.belief_module - INFO - 用户 'user_51' 的短期记忆中未找到显著议题簇
2025-08-26 21:43:41,568 - Engine - INFO - Cognitive processing for user 'user_51' finished.
2025-08-26 21:43:41,568 - Engine - INFO - ----- Action 'READ_POST' for user 'user_51' processed successfully. -----
2025-08-26 21:43:41,568 - Simulator - INFO - Processing event from queue: READ_POST for user user_46
2025-08-26 21:43:41,569 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_46' -----
2025-08-26 21:43:48,913 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_46_55284624).
2025-08-26 21:43:48,913 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:43:51,742 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:43:51,742 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:43:51,742 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:43:51,743 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_46'...
2025-08-26 21:43:51,743 - src.modules.belief_module - INFO - 开始处理用户 'user_46' 的认知流程...
2025-08-26 21:43:51,758 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:43:51,758 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:43:51,758 - src.modules.belief_module - INFO - 用户 'user_46' 的短期记忆中未找到显著议题簇
2025-08-26 21:43:51,758 - Engine - INFO - Cognitive processing for user 'user_46' finished.
2025-08-26 21:43:51,758 - Engine - INFO - ----- Action 'READ_POST' for user 'user_46' processed successfully. -----
2025-08-26 21:43:51,759 - Simulator - INFO - Processing event from queue: READ_POST for user user_94
2025-08-26 21:43:51,759 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_94' -----
2025-08-26 21:43:58,998 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_94_de109860).
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_62'...
MemoryModule: Successfully created memory 'mem_user_62_70e9c2f4'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_62_70e9c2f4)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_62 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_62 的认知处理...

【认知处理】开始处理用户 user_62 的认知流程
【阶段1+2】获取用户 user_62 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_62'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_62 的认知处理完成
【引擎】用户 user_62 的 READ_POST 操作处理成功
【步骤 5/5】完成: 求证队列处理完成
【引擎】用户 user_62 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_62)
【处理事件】从队列中处理事件: READ_POST (用户 user_45)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_45 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_45'...
MemoryModule: Successfully created memory 'mem_user_45_38dccfb1'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_45_38dccfb1)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_45 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_45 的认知处理...

【认知处理】开始处理用户 user_45 的认知流程
【阶段1+2】获取用户 user_45 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_45'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_45 的认知处理完成
【引擎】用户 user_45 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_45)
【处理事件】从队列中处理事件: READ_POST (用户 user_21)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_21 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_21'...
MemoryModule: Successfully created memory 'mem_user_21_884940e9'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_21_884940e9)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_21 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_21 的认知处理...

【认知处理】开始处理用户 user_21 的认知流程
【阶段1+2】获取用户 user_21 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_21'...
【阶段1+2】对短期记忆进行聚类分析
【阶段3】开始信念更新阶段，发现 1 个显著簇
【阶段3】处理第 1 个议题簇，包含 2 条记忆
【阶段3】正在更新或创建信念...
【阶段3】信念更新成功，信念ID: belief_user_21_2ff77a97
【步骤 4/4】完成: 用户 user_21 的认知处理完成
【步骤 5/5】处理求证队列，队列大小: 1
【引擎】开始处理用户 user_21 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_21'...
MemoryModule: Successfully created memory 'mem_user_21_9b8255b8'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_21_9b8255b8)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_21 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_21 的认知处理...

【认知处理】开始处理用户 user_21 的认知流程
【阶段1+2】获取用户 user_21 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_21'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_21 的认知处理完成
【引擎】用户 user_21 的 READ_POST 操作处理成功
【步骤 5/5】完成: 求证队列处理完成
【引擎】用户 user_21 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_21)
【处理事件】从队列中处理事件: READ_POST (用户 user_51)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_51 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_51'...
MemoryModule: Successfully created memory 'mem_user_51_449e4e6b'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_51_449e4e6b)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_51 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_51 的认知处理...

【认知处理】开始处理用户 user_51 的认知流程
【阶段1+2】获取用户 user_51 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_51'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_51 的认知处理完成
【引擎】用户 user_51 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_51)
【处理事件】从队列中处理事件: READ_POST (用户 user_46)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_46 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_46'...
MemoryModule: Successfully created memory 'mem_user_46_55284624'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_46_55284624)
【步骤 2/4】正在更新内容状态...
【步骤 2/4】完成: 内容状态已更新
【步骤 3/4】正在处理社交通知...
【通知】检查用户 user_46 的 READ_POST 操作是否需要发送通知...
【步骤 3/4】完成: 通知处理完成
【步骤 4/4】开始用户 user_46 的认知处理...

【认知处理】开始处理用户 user_46 的认知流程
【阶段1+2】获取用户 user_46 的短期记忆队列
MemoryModule: Getting STM queue for user 'user_46'...
【阶段1+2】对短期记忆进行聚类分析
【阶段1+2】结果: 未找到显著的议题簇，终止处理
【步骤 4/4】完成: 用户 user_46 的认知处理完成
【引擎】用户 user_46 的 READ_POST 操作处理成功
【处理成功】事件处理成功: READ_POST (用户 user_46)
【处理事件】从队列中处理事件: READ_POST (用户 user_94)，浏览帖子 post_851d5fa2："您关注的用户 user_23 发布了新帖子:

最近看到关于婚姻和继承的讨论越来越热闹，有些说法甚至..."
【引擎】开始处理用户 user_94 的 READ_POST 操作
【步骤 1/4】正在创建记忆...
MemoryModule: Creating memory for user 'user_94'...
MemoryModule: Successfully created memory 'mem_user_94_de109860'
【步骤 1/4】成功: 记忆创建成功 (ID: mem_user_94_de109860)
2025-08-26 21:43:58,998 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:44:01,677 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:44:01,677 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:44:01,677 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:44:01,678 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_94'...
2025-08-26 21:44:01,678 - src.modules.belief_module - INFO - 开始处理用户 'user_94' 的认知流程...
2025-08-26 21:44:01,693 - src.modules.belief_module - INFO - 正在对 1 条短期记忆进行聚类分析...
2025-08-26 21:44:01,693 - src.modules.belief_module - INFO - 有效记忆数量 (1) 过少，不进行聚类
2025-08-26 21:44:01,693 - src.modules.belief_module - INFO - 用户 'user_94' 的短期记忆中未找到显著议题簇
2025-08-26 21:44:01,693 - Engine - INFO - Cognitive processing for user 'user_94' finished.
2025-08-26 21:44:01,693 - Engine - INFO - ----- Action 'READ_POST' for user 'user_94' processed successfully. -----
2025-08-26 21:44:01,694 - Simulator - INFO - Processing event from queue: READ_POST for user user_43
2025-08-26 21:44:01,694 - Engine - INFO - ----- Processing action 'READ_POST' for user 'user_43' -----
2025-08-26 21:44:08,857 - Engine - INFO - Step 1/4: Memory created successfully (ID: mem_user_43_867a8528).
2025-08-26 21:44:08,858 - Engine - INFO - Step 2/4: Updating content state for action 'READ_POST'...
2025-08-26 21:44:14,041 - src.modules.content_module - INFO - Incremented view count for post 'post_851d5fa2'.
2025-08-26 21:44:14,041 - Engine - INFO - Step 3/4: Checking for notifications...
2025-08-26 21:44:14,041 - Engine - INFO - No notification needed for this action type.
2025-08-26 21:44:14,041 - Engine - INFO - Step 4/4: Starting cognitive processing for user 'user_43'...
2025-08-26 21:44:14,041 - src.modules.belief_module - INFO - 开始处理用户 'user_43' 的认知流程...
2025-08-26 21:44:14,066 - src.modules.belief_module - INFO - 正在对 2 条短期记忆进行聚类分析...
2025-08-26 21:44:14,066 - src.modules.belief_module - INFO - 有效记忆数量为2，直接作为单个簇处理
2025-08-26 21:44:14,071 - src.modules.belief_module - INFO - 找到 1 个显著议题簇
2025-08-26 21:44:14,071 - src.modules.belief_module - INFO - 正在从记忆簇 'cluster_f1fa2398' 更新或创建信念...
2025-08-26 21:44:14,153 - src.modules.belief_module - INFO - 找到最相似的记忆命题 (索引 1), 最大相似度: 0.3481
2025-08-26 21:44:14,153 - src.modules.belief_module - INFO - 对应的最相似信念: 'belief_1756214095226861'
2025-08-26 21:44:14,154 - src.modules.belief_module - INFO - 选择的备选信念命题: '最近跟家人聊结婚和分家产的事，才意识到以前啥都摆在桌上说，现在可不一定安全了。户口本、房产证、银行流水这些，以前觉得一家人不用避讳，可现在要是被泄露，麻烦可大了。听说有人婚前聊天记录被拿去当财产分割的证据，真是吓一跳。现在结婚讲信任，但得先有数据安全做底子。我劝家里人，重要信息别随便拍照发群里，也别随便给那些“家庭管理”APP授权。不是不信任人，是数据一出去就收不回来了。保护隐私，其实才是对家人更负责。'
2025-08-26 21:44:14,154 - src.modules.belief_module - INFO - embedding相似度 (0.3481) 低于阈值 (0.9)，创建节点
2025-08-26 21:44:14,154 - src.modules.belief_module - INFO - 使用多线程模式聚合证据
2025-08-26 21:44:14,154 - src.modules.belief_module - INFO - 正在聚合记忆簇 'cluster_f1fa2398' 的证据（多线程模式）...
2025-08-26 21:44:20,439 - src.modules.belief_module - INFO - 聚合结果（多线程模式） - 支持权重: 0.6575, 反对权重: 0.0000
2025-08-26 21:44:20,439 - src.modules.belief_module - INFO - 正在为用户 'user_43' 创建新信念: '最近跟家人聊结婚和分家产的事，才意识到以前啥都摆在桌上说，现...'
2025-08-26 21:44:23,815 - src.modules.belief_module - INFO - 使用多线程模式建立信念关系
2025-08-26 21:44:23,815 - src.modules.belief_module - INFO - 正在为信念 'belief_user_43_c1b92ee7' 建立关系网络（多线程模式）...
2025-08-26 21:44:23,834 - src.modules.belief_module - INFO - 找到 5 个现有信念，开始多线程处理...
2025-08-26 21:44:44,219 - src.modules.belief_module - INFO - 建立关系 'belief_user_43_c1b92ee7' -反驳/diminishes-> 'belief_1756214095226861' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:44:47,555 - src.modules.belief_module - INFO - 建立关系 'belief_1756214095226861' -支持/imply-> 'belief_user_43_c1b92ee7' (权重: 0.75, 置信度: 0.85)
2025-08-26 21:44:53,736 - src.modules.belief_module - INFO - 为信念 'belief_user_43_c1b92ee7' 建立了 1 个关系（多线程模式）
2025-08-26 21:44:53,737 - src.modules.belief_module - INFO - 成功创建信念 'belief_user_43_c1b92ee7':
2025-08-26 21:44:53,737 - src.modules.belief_module - INFO -   - 真实度: 0.6575
2025-08-26 21:44:53,737 - src.modules.belief_module - INFO -   - 置信度: 0.3132
2025-08-26 21:44:53,741 - src.modules.belief_module - INFO - 信念 'belief_user_43_c1b92ee7' 的置信度 (0.3132) 低于求证阈值 (0.3500)
2025-08-26 21:44:53,741 - src.modules.belief_module - INFO - 正在为用户 'user_43' 触发对信念 'belief_user_43_c1b92ee7' 的求证机制...
2025-08-26 21:44:53,741 - src.modules.belief_module - INFO - 从search.json中选择的搜索查询: 关于财产权，你必须知道的核心：所有权登记 > 一切口头承诺
2025-08-26 21:44:53,742 - src.modules.belief_module - INFO - 搜索结果:
2025-08-26 21:44:53,742 - src.modules.belief_module - INFO - 1. 大家别慌，我们国家的法律基石是很稳固的。关于财产，尤其是房子这种大事，核心就一条：不动产登记簿上写谁，就是谁的。这叫物权公示公信原则。别说同居一年，就是同居十年，只要房本没你名，法律上你就没份。所有让你焦虑的说法，都绕不开这个基本原则。所以，守好你的产权证，比什么都强。#物权法 #不动产登记 #硬核知识
2025-08-26 21:44:53,742 - src.modules.belief_module - INFO - 2. 这个谣言虽然是假的，但它能火爆全网，恰恰说明了一个现实问题：我国对非婚同居关系的法律保护确实存在空白。现实中，一方为家庭付出多年，但因没有一纸婚书，分手或一方离世后权益无法保障的案例比比皆是。我们不应止于辟谣，更应思考，如何在保护私有财产权和维护传统婚姻制度之间，为“事实伴侣”提供一条人道的、有限的救济途径？比如在分割同居期间共同财产（析产）或请求扶养补偿方面，是否可以有更明确的规定？#法律思考 #非婚同居 #民法典
2025-08-26 21:44:53,742 - src.modules.belief_module - INFO - 生成了求证搜索行为: '关于财产权，你必须知道的核心：所有权登记 > 一切口头承诺'
2025-08-26 21:44:53,742 - src.modules.belief_module - INFO - 将求证搜索行为加入处理队列...
2025-08-26 21:44:53,742 - Engine - INFO - Added verification action to queue. Queue size: 1
2025-08-26 21:44:53,742 - src.modules.belief_module - INFO - 求证搜索行为已加入队列，将在当前认知处理完成后执行
2025-08-26 21:44:53,742 - src.modules.belief_module - INFO - 已触发信念求证, 查询: '关于财产权，你必须知道的核心：所有权登记 > 一切口头承诺'
2025-08-26 21:44:53,742 - src.modules.belief_module - INFO - 认知冲击 (0.6575) 超过怀疑阈值 (0.5300)
2025-08-26 21:44:53,743 - src.modules.agent_module - INFO - 正在为用户 'user_43' 演化特质，认知冲击为: 0.6575411068612773, 内容情感强度: 0.44375
2025-08-26 21:44:53,745 - src.modules.agent_module - INFO - 基于内容情感强度 0.444 调整情绪波动性变化: -0.0059
2025-08-26 21:44:56,663 - src.modules.agent_module - INFO - 用户 'user_43' 的特质已更新:
2025-08-26 21:44:56,663 - src.modules.agent_module - INFO -   - 怀疑主义得分: 0.5958
2025-08-26 21:44:56,664 - src.modules.agent_module - INFO -   - 验证阈值: 0.3829
2025-08-26 21:44:56,664 - src.modules.agent_module - INFO -   - 情绪波动性: 0.2741
2025-08-26 21:44:56,664 - src.modules.belief_module - INFO - 正在传播信念 'belief_user_43_c1b92ee7' 的变化，认知冲击为 0.6575411068612773...
2025-08-26 21:44:56,664 - src.modules.belief_module - INFO - 信念 'belief_user_43_c1b92ee7' 没有关联边，无需传播
2025-08-26 21:44:56,664 - src.modules.belief_module - INFO - 已触发特质演化和信念传播, 冲击大小: 0.6575, 情感强度: 0.444
2025-08-26 21:44:56,664 - src.modules.belief_module - INFO - 将来自簇 cluster_f1fa2398 的 2 条记忆移入历史...
