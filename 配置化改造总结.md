# 主题配置化改造总结

## 问题描述

原系统中存在多处硬编码"婚姻和继承"主题的问题，导致修改主题时需要在多个文件中进行修改，维护困难。

## 解决方案

将硬编码的主题信息提取到配置文件中，实现配置化管理。

## 修改内容

### 1. 配置文件修改 (`config/config.py`)

**新增配置项：**
```python
# 主题配置
TOPIC_NAME = os.getenv("TOPIC_NAME", "婚姻和继承")  # 当前讨论主题
TOPIC_DATA_DIR = os.getenv("TOPIC_DATA_DIR", "婚姻和继承")  # 主题数据目录名
```

**新增配置方法：**
- `get_topic_config()`: 获取主题配置
- `get_topic_data_paths()`: 获取主题数据文件路径

### 2. 代码文件修改

#### `src/modules/ai_service.py`
- **修改位置**: 第560行
- **原代码**: `topic = "婚姻和继承"`
- **新代码**: `topic = Config.TOPIC_NAME`
- **说明**: AI服务生成内容时使用配置化的主题名称

#### `src/modules/belief_module.py`
- **修改位置**: 第1583行
- **原代码**: 硬编码文件路径 `'lib', '婚姻和继承', 'search.json'`
- **新代码**: 使用配置化路径 `Config.get_topic_data_paths()['search_file']`
- **说明**: 信念模块搜索功能使用配置化的数据文件路径

#### `src/modules/user_management.py`
- **修改位置**: 第38-39行
- **原代码**: 硬编码文件路径 `"lib", "婚姻和继承", "belief.json"`
- **新代码**: 使用配置化路径 `Config.get_topic_data_paths()['belief_file']`
- **说明**: 用户管理模块加载信念数据时使用配置化路径

#### `scripts/initialize_db.py`
- **修改位置**: 第59-61行
- **原代码**: 硬编码文件路径
- **新代码**: 使用配置化路径 `Config.get_topic_data_paths()`
- **说明**: 数据库初始化脚本使用配置化的数据文件路径

#### `experiment/structure/community_based_initialize_db.py`
- **修改位置**: 第84-86行
- **原代码**: 硬编码文件路径
- **新代码**: 使用配置化路径 `Config.get_topic_data_paths()`
- **说明**: 社区结构初始化脚本使用配置化的数据文件路径

## 使用方法

### 方法1: 环境变量配置（推荐）
```bash
export TOPIC_NAME="新主题名称"
export TOPIC_DATA_DIR="new_topic_directory"
```

### 方法2: 修改配置文件
直接编辑 `config/config.py` 中的默认值。

## 数据文件要求

新主题需要在 `lib/` 目录下创建对应文件夹，包含：
- `belief.json`: 信念数据
- `post.json`: 帖子数据  
- `search.json`: 搜索结果数据

## 测试验证

已通过以下测试验证配置化改造的正确性：
1. ✅ 配置文件功能测试
2. ✅ 环境变量覆盖测试
3. ✅ AI服务主题配置测试
4. ✅ 数据文件路径解析测试

## 优势

1. **维护性提升**: 修改主题只需要修改配置，无需改动代码
2. **灵活性增强**: 支持环境变量配置，适合不同部署环境
3. **扩展性改善**: 添加新主题只需要准备数据文件和设置配置
4. **一致性保证**: 所有模块统一使用配置化的主题信息

## 注意事项

1. 修改配置后需要重启应用程序
2. 确保新主题的数据文件存在且格式正确
3. 所有JSON文件必须使用UTF-8编码
4. 保持数据文件的JSON结构与原始文件一致

## 文档

已创建 `TOPIC_CONFIG_README.md` 详细使用指南，包含：
- 配置方法说明
- 使用示例
- 故障排除指南
- 最佳实践建议

## 总结

通过这次配置化改造，成功解决了硬编码主题的问题，大大提升了系统的灵活性和可维护性。现在可以轻松地切换不同的讨论主题，为系统的扩展和部署提供了更好的支持。
