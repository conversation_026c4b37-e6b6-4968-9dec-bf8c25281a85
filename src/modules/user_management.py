#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import os
import json
import random
import time
from typing import Dict, List, Any
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
from src.database import ES_CLIENT
from config.config import Config

logger = logging.getLogger("UserManagement")

class UserManagementModule:
    """
    用户管理模块，负责用户的创建、信念分配、关系建立等功能
    """
    
    def __init__(self, engine):
        """
        初始化用户管理模块
        
        Args:
            engine: 引擎实例
        """
        self.engine = engine
        logger.info("UserManagementModule initialized.")
    
    def _load_beliefs_data(self) -> List[Dict[str, Any]]:
        """加载信念数据文件"""
        try:
            topic_paths = Config.get_topic_data_paths()
            beliefs_file_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                topic_paths['belief_file']
            )
            with open(beliefs_file_path, 'r', encoding='utf-8') as file:
                return json.load(file)
        except Exception as e:
            logger.error(f"加载信念数据失败: {e}")
            return []

    def _add_followers_for_intellectuals(self, intellectual_ids: List[str]):
        """为知识分子用户添加粉丝（被关注用户）"""
        logger.info(f"开始为 {len(intellectual_ids)} 个知识分子用户添加粉丝...")

        # 获取所有现有用户ID（排除刚创建的知识分子）
        try:
            all_user_ids = self.engine.agent_module.get_all_agent_ids()
            potential_followers = [uid for uid in all_user_ids if uid not in intellectual_ids]

            if not potential_followers:
                logger.warning("没有找到可以作为粉丝的现有用户")
                return

            logger.info(f"找到 {len(potential_followers)} 个潜在粉丝用户")

        except Exception as e:
            logger.error(f"获取现有用户列表失败: {e}")
            return

        # 为每个知识分子添加粉丝
        for intellectual_id in intellectual_ids:
            try:
                # 知识分子应该有较多粉丝，选择当前用户数的一半作为粉丝数量
                target_followers = len(potential_followers) // 2
                # 确保至少有5个粉丝，最多不超过可用用户数
                num_followers = min(max(target_followers, 5), len(potential_followers))

                # 随机选择粉丝
                selected_followers = random.sample(potential_followers, num_followers)

                logger.info(f"为知识分子 {intellectual_id} 添加 {num_followers} 个粉丝...")

                # 建立关注关系
                successful_follows = 0
                for follower_id in selected_followers:
                    try:
                        # 使用AgentModule的update_user_follow方法建立关注关系
                        self.engine.agent_module.update_user_follow(follower_id, intellectual_id, follow=True)
                        successful_follows += 1

                    except Exception as e:
                        logger.warning(f"用户 {follower_id} 关注 {intellectual_id} 失败: {e}")
                        continue

                logger.info(f"知识分子 {intellectual_id} 成功获得 {successful_follows} 个粉丝")

            except Exception as e:
                logger.error(f"为知识分子 {intellectual_id} 添加粉丝时出错: {e}")

        logger.info("知识分子粉丝添加完成")

    def _build_belief_relations_for_user(self, user_id: str, belief_ids: List[str]):
        """为指定用户的信念建立关系图，参考initialize_db.py的实现"""
        logger.info(f"为用户 {user_id} 的信念建立关系...")

        # 获取该用户所有信念的内容
        beliefs = {}
        for belief_id in belief_ids:
            try:
                belief_doc = ES_CLIENT.get(index="beliefs", id=belief_id)["_source"]
                beliefs[belief_id] = belief_doc
            except Exception as e:
                logger.error(f"获取信念 {belief_id} 失败: {e}")
                continue

        # 为每个信念建立与其他信念的关系
        for i, belief1_id in enumerate(belief_ids):
            if belief1_id not in beliefs:
                continue

            belief1 = beliefs[belief1_id]

            for j, belief2_id in enumerate(belief_ids):
                if i == j or belief2_id not in beliefs:
                    continue

                belief2 = beliefs[belief2_id]

                # 尝试使用AI服务确定两个信念之间的关系
                try:
                    relation_info = None
                    try:
                        # 尝试使用AI服务判断关系
                        relation_info = self.engine.ai_service.determine_detailed_belief_relationship(
                            belief1["belief_summary"], belief2["belief_summary"])
                        logger.debug(f"成功获取信念关系: {relation_info}")
                    except Exception as e:
                        logger.warning(f"获取信念关系失败，使用随机值: {e}")
                        # AI服务失败时的备选方案
                        relation_category = random.choice(["支持", "反驳", "无关"])
                        if relation_category == "支持":
                            relation_type = random.choice(["蕴含/前提", "因果支持", "证据支持", "相关"])
                            weight = round(random.uniform(0.5, 1.0), 2)
                        elif relation_category == "反驳":
                            relation_type = random.choice(["排斥", "削弱", "反证"])
                            weight = round(random.uniform(0.5, 1.0), 2)
                        else: # 无关
                            relation_type = None
                            weight = 0.0

                        relation_info = {
                            "relation_category": relation_category,
                            "relation_type": relation_type,
                            "weight": weight,
                            "confidence": round(random.uniform(0.5, 1.0), 2),
                            "explanation": "随机生成的关系"
                        }

                    # 如果关系为"无关"或类型为None，则不创建关系边
                    if relation_info.get("relation_category") == "无关" or not relation_info.get("relation_type"):
                        logger.debug(f"信念 {belief1_id} 和 {belief2_id} 无关，跳过创建关系。")
                        continue

                    # 确保使用正确的字段名
                    relation_type = relation_info["relation_type"]
                    relation_strength = relation_info["weight"]

                    # 更新第一个信念的关系边
                    new_edge = {
                        "target_node_id": belief2_id,
                        "relation_type": relation_type,
                        "relation_category": relation_info["relation_category"],
                        "weight": relation_strength,
                        "confidence": relation_info["confidence"],
                        "explanation": relation_info["explanation"]
                    }

                    # 获取当前信念的关系边
                    current_edges = belief1.get("relation_edges", [])
                    current_edges.append(new_edge)

                    # 更新信念文档
                    ES_CLIENT.update(
                        index="beliefs",
                        id=belief1_id,
                        doc={"relation_edges": current_edges}
                    )

                    logger.debug(f"为信念 {belief1_id} 添加了与 {belief2_id} 的关系: {relation_type}")

                except Exception as e:
                    logger.error(f"建立信念关系时发生错误: {e}")

        logger.info(f"为用户 {user_id} 的信念建立关系完成")

    def _add_beliefs_for_user(self, user_id: str, beliefs_data: List[Dict[str, Any]], is_intellectual: bool = True):
        """为指定用户添加信念，参考initialize_db.py的实现"""
        logger.info(f"为用户 {user_id} 添加信念...")

        # 使用时间戳和用户ID生成唯一的信念ID，避免冲突
        base_timestamp = int(time.time() * 1000)  # 毫秒级时间戳

        # 为用户分配3-6个信念
        num_beliefs = random.randint(8, 10)

        # 随机选择信念
        if len(beliefs_data) < num_beliefs:
            chosen_beliefs = random.sample(range(1, len(beliefs_data) + 1), len(beliefs_data))
        else:
            chosen_beliefs = random.sample(range(1, len(beliefs_data) + 1), num_beliefs)

        user_belief_ids = []

        for i, belief_idx in enumerate(chosen_beliefs):
            belief = next((b for b in beliefs_data if b["id"] == belief_idx), None)
            if not belief:
                continue

            # 使用用户ID、时间戳和序号生成唯一的信念ID
            belief_id = f"belief_{user_id}_{base_timestamp}_{i}"

            # 根据用户类型设置信念来源权重
            if is_intellectual:
                # 知识分子更可能从proposition读取
                source_weights = {"proposition": 0.7, "oppositional_view": 0.2, "vernacular_saying": 0.1}
            else:
                # 普通用户更平均地选择信念来源
                source_weights = {"proposition": 0.1, "oppositional_view": 0.2, "vernacular_saying": 0.7}

            # 根据权重随机选择信念来源
            sources = list(source_weights.keys())
            weights = list(source_weights.values())
            belief_source = random.choices(sources, weights=weights, k=1)[0]

            # 获取对应来源的信念内容
            belief_summary = belief[belief_source]

            # 根据用户类型和信念来源设置真实度和置信度
            if is_intellectual:
                # 知识分子的信念真实度和置信度通常较高
                if belief_source == "proposition":
                    veracity_score = random.uniform(0.6, 1.0)
                    confidence = random.uniform(0.6, 0.9)
                else:
                    veracity_score = random.uniform(0.4, 0.9)
                    confidence = random.uniform(0.5, 0.8)
            else:
                # 普通用户的信念真实度和置信度相对较低
                if belief_source == "proposition":
                    veracity_score = random.uniform(0.4, 0.8)
                    confidence = random.uniform(0.4, 0.7)
                else:
                    veracity_score = random.uniform(0.2, 0.7)
                    confidence = random.uniform(0.3, 0.6)

            # 使用AI服务获取情感倾向
            try:
                emotional_disposition = self.engine.ai_service.analyze_emotional_impact(belief_summary)
                logger.debug(f"成功获取信念'{belief_summary[:30]}...'的情感向量")

                # 确保所有必需的情感维度都存在，如果缺失则使用默认值
                required_emotions = ["joy", "sadness", "anger", "fear", "surprise", "trust", "disgust", "anticipation"]
                for emotion in required_emotions:
                    if emotion not in emotional_disposition:
                        emotional_disposition[emotion] = 0.5
                    # 确保值在0-1范围内
                    emotional_disposition[emotion] = max(0.0, min(1.0, float(emotional_disposition[emotion])))
                    emotional_disposition[emotion] = round(emotional_disposition[emotion], 2)

            except Exception as e:
                logger.warning(f"获取情感向量失败，使用随机值: {e}")
                # 如果AI服务失败，使用随机值作为备选
                emotional_disposition = {
                    "joy": round(random.uniform(0, 1), 2),
                    "sadness": round(random.uniform(0, 1), 2),
                    "anger": round(random.uniform(0, 1), 2),
                    "fear": round(random.uniform(0, 1), 2),
                    "surprise": round(random.uniform(0, 1), 2),
                    "trust": round(random.uniform(0, 1), 2),
                    "disgust": round(random.uniform(0, 1), 2),
                    "anticipation": round(random.uniform(0, 1), 2)
                }

            # 使用AI服务获取嵌入向量
            try:
                embedding = self.engine.ai_service.get_embedding(belief_summary)
                logger.debug(f"成功获取信念'{belief_summary[:30]}...'的嵌入向量")
            except Exception as e:
                logger.warning(f"获取嵌入向量失败，使用随机值: {e}")
                embedding = [random.uniform(-1, 1) for _ in range(4096)]

            # 创建信念文档
            belief_doc = {
                "node_id": belief_id,
                "owner_user_id": user_id,
                "belief_summary": belief_summary,
                "embedding": embedding,
                "veracity_score": veracity_score,
                "confidence": confidence,
                "emotional_disposition": emotional_disposition,
                "evidence_memory_ids": [],
                "relation_edges": []
            }

            try:
                # 添加到ES
                ES_CLIENT.index(index="beliefs", id=belief_id, document=belief_doc)
                user_belief_ids.append(belief_id)
                logger.debug(f"成功为用户 {user_id} 添加信念: {belief_id}")
            except Exception as e:
                logger.error(f"添加信念 {belief_id} 失败: {e}")

        logger.info(f"为用户 {user_id} 成功添加了 {len(user_belief_ids)} 个信念")

        # 构建信念图（如果有多个信念）
        if len(user_belief_ids) > 1:
            self._build_belief_relations_for_user(user_id, user_belief_ids)

        return user_belief_ids

    def add_intellectual_users(self, num_intellectuals: int, start_user_id: int = None) -> List[str]:
        """
        添加指定数量的知识分子用户到系统中，并为其添加信念

        Args:
            num_intellectuals: 要添加的知识分子数量
            start_user_id: 起始用户ID编号，如果为None则自动计算

        Returns:
            List[str]: 新创建的用户ID列表
        """
        logger.info(f"开始添加 {num_intellectuals} 个知识分子用户...")

        # 加载信念数据
        beliefs_data = self._load_beliefs_data()
        if not beliefs_data:
            logger.error("无法加载信念数据，将跳过信念添加")

        # 如果没有指定起始ID，则获取当前最大用户ID
        if start_user_id is None:
            try:
                all_user_ids = self.engine.agent_module.get_all_agent_ids()
                # 提取数字部分并找到最大值
                max_id = 0
                for user_id in all_user_ids:
                    if user_id.startswith("user_"):
                        try:
                            num = int(user_id.split("_")[1])
                            max_id = max(max_id, num)
                        except (IndexError, ValueError):
                            continue
                start_user_id = max_id + 1
            except Exception as e:
                logger.warning(f"无法获取现有用户ID，使用默认起始ID 1000: {e}")
                start_user_id = 1000

        new_user_ids = []

        for i in range(num_intellectuals):
            user_id = f"user_{start_user_id + i}"

            # 生成大五人格特质
            ocean_personality = {
                "O": round(random.uniform(0.6, 0.9), 2),  # 知识分子通常开放性较高
                "C": round(random.uniform(0.5, 0.9), 2),  # 尽责性
                "E": round(random.uniform(0.2, 0.8), 2),  # 外向性
                "A": round(random.uniform(0.3, 0.8), 2),  # 宜人性
                "N": round(random.uniform(0.2, 0.7), 2)   # 神经质
            }

            # 知识分子的认知特质 - 较高的怀疑分数和验证阈值
            cognitive_traits = {
                "skepticism_score": round(random.uniform(0.6, 0.9), 2),
                "verification_threshold": round(random.uniform(0.6, 0.9), 2),
                "emotional_volatility": round(random.uniform(0.2, 0.7), 2),
                "is_intellectual": True  # 标记为知识分子
            }

            # 创建用户数据
            user_data = {
                "user_id": user_id,
                "user_group": "intellectual",  # 标记为知识分子群体
                "ocean_personality": ocean_personality,
                "cognitive_traits": cognitive_traits,
                "followed_posts": [],
                "followed_users": [],
                "followers": [],
                "posts": [],
                "comments": []
            }

            try:
                # 将用户添加到数据库
                ES_CLIENT.index(index="agents", id=user_id, document=user_data)
                new_user_ids.append(user_id)
                logger.info(f"成功添加知识分子用户: {user_id}")

                # 为新用户添加信念
                if beliefs_data:
                    self._add_beliefs_for_user(user_id, beliefs_data, is_intellectual=True)

            except Exception as e:
                logger.error(f"添加用户 {user_id} 失败: {e}")

        # 为知识分子用户添加粉丝（被关注用户）
        if new_user_ids:
            self._add_followers_for_intellectuals(new_user_ids)

        # 添加完所有用户后，刷新索引确保数据可被查询
        try:
            ES_CLIENT.indices.refresh(index="beliefs")
            ES_CLIENT.indices.refresh(index="agents")
            logger.info("已刷新Elasticsearch索引，确保新数据可被查询")
        except Exception as e:
            logger.warning(f"刷新索引失败: {e}")

        logger.info(f"成功添加了 {len(new_user_ids)} 个知识分子用户")
        return new_user_ids
