# src/modules/belief_module.py

from typing import Dict, Any, List, Tuple, Optional
import datetime
import uuid
import math
import logging
import json
import random
import os
import concurrent.futures
import threading
from functools import partial

from src.database import ES_CLIENT
from config.config import Config
from .agent_module import AgentModule
from .memory_module import MemoryModule
from .ai_service import AIService
import numpy as np
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from sklearn.metrics.pairwise import cosine_similarity

BELIEFS_INDEX = "beliefs"
logger = logging.getLogger(__name__)

class BeliefModule:
    """
    信念模块，封装了所有与智能体认知、信念网络相关的功能。
    这包括短期记忆处理、信念更新、求证机制和自我演化。
    """

    def __init__(self, agent_module: AgentModule, memory_module: MemoryModule, ai_service: AIService):
        """
        初始化信念模块。

        Args:
            agent_module (AgentModule): AgentModule的实例，用于访问和更新智能体特质。
        """
        self.agent_module = agent_module
        self.memory_module = memory_module
        self.ai_service = ai_service
        self._engine_instance = None  # 将在set_engine_instance中设置

        # 获取多线程配置
        self.multithreading_config = Config.get_multithreading_config()
        self._thread_lock = threading.Lock()  # 用于线程安全的操作

        logger.info("BeliefModule initialized with dependencies.")
        logger.info(f"多线程配置: {self.multithreading_config}")

    def set_engine_instance(self, engine_instance):
        """
        设置引擎实例，用于求证机制的闭环反馈。

        Args:
            engine_instance: Engine实例
        """
        self._engine_instance = engine_instance

    def process_agent_cognition(self, user_id: str, conflict_threshold: float = 0.05) -> Dict[str, Any]:
        print(f"\n【认知处理】开始处理用户 {user_id} 的认知流程")
        """
        处理智能体的完整认知流程，包括STM处理、信念更新、求证机制和自我演化。
        
        Args:
            user_id: 用户ID
            conflict_threshold: 冲突度阈值，只有超过此阈值的簇才会被处理
        
        Returns:
            处理结果的摘要信息
        """
        logger.info(f"开始处理用户 '{user_id}' 的认知流程...")
        
        try:
            # 阶段一 & 二: STM处理与议题识别
            print(f"【阶段1+2】获取用户 {user_id} 的短期记忆队列")
            stm_queue = self.memory_module.get_stm_queue(user_id)

            # 不需要额外过滤，因为get_stm_queue已经从活跃记忆索引获取
            # 如果记忆已被移动到历史，它们不会出现在STM队列中

            if not stm_queue:
                print("【阶段1+2】结果: 短期记忆队列为空，终止处理")
                logger.info(f"用户 '{user_id}' 的短期记忆队列为空，无需处理")
                return {"status": "no_stm", "user_id": user_id}
            
            print("【阶段1+2】对短期记忆进行聚类分析")
            significant_clusters = self.find_significant_clusters(stm_queue, conflict_threshold)
            if not significant_clusters:
                print("【阶段1+2】结果: 未找到显著的议题簇，终止处理")
                logger.info(f"用户 '{user_id}' 的短期记忆中未找到显著议题簇")
                return {"status": "no_clusters", "user_id": user_id}
            
            # 阶段三: 条件化信念更新
            print(f"【阶段3】开始信念更新阶段，发现 {len(significant_clusters)} 个显著簇")
            updated_beliefs = []
            actions_taken = []
            for i, cluster in enumerate(significant_clusters):
                print(f"【阶段3】处理第 {i+1} 个议题簇，包含 {len(cluster.get('memories', []))} 条记忆")
                # 更新或创建信念，并获取更新结果
                print("【阶段3】正在更新或创建信念...")
                updated_belief, deltaV = self.update_or_create_belief_from_cluster(user_id, cluster)
                
                if not updated_belief:
                    print("【阶段3】信念更新失败，跳过处理")
                    continue

                # 获取智能体当前特质
                agent_traits = self.agent_module.get_agent_traits(user_id)
                if not agent_traits:
                    logger.error(f"无法获取用户 '{user_id}' 的特质，跳过后续处理")
                    continue

                action_detail = {"belief_id": updated_belief["node_id"], "action_taken": "none"}
                print(f"【阶段3】信念更新成功，信念ID: {updated_belief['node_id']}")
                
                # 阶段四: 求证机制
                if updated_belief["confidence"] < agent_traits["verification_threshold"]:
                    logger.info(f"信念 '{updated_belief['node_id']}' 的置信度 ({updated_belief['confidence']:.4f}) 低于求证阈值 ({agent_traits['verification_threshold']:.4f})")
                    action = self.trigger_verification_action(user_id, updated_belief)
                    action_detail["action_taken"] = "trigger_verification"
                    action_detail["action_details"] = {"query": action["source"]["search_query"]}
                    logger.info(f"已触发信念求证, 查询: '{action['source']['search_query']}'")
                
                # 阶段五: 自我演化
                if deltaV > agent_traits["skepticism_score"]:
                    logger.info(f"认知冲击 ({deltaV:.4f}) 超过怀疑阈值 ({agent_traits['skepticism_score']:.4f})")

                    # 计算当前cluster中记忆的平均情感强度
                    content_emotional_intensity = self._calculate_cluster_emotional_intensity(cluster)

                    # 特质演化
                    self.agent_module.evolve_agent_traits(user_id, deltaV, content_emotional_intensity)
                    # 级联更新
                    self.propagate_belief_changes(updated_belief, deltaV)
                    action_detail["action_taken"] = "evolve_and_propagate"
                    action_detail["action_details"] = {"delta_V": deltaV, "emotional_intensity": content_emotional_intensity}
                    logger.info(f"已触发特质演化和信念传播, 冲击大小: {deltaV:.4f}, 情感强度: {content_emotional_intensity:.3f}")
                
                # 将处理过的记忆移入历史
                logger.info(f"将来自簇 {cluster['cluster_id']} 的 {len(cluster['memories'])} 条记忆移入历史...")
                moved_count = 0
                for memory in cluster['memories']:
                    memory_id = memory.get("memory_id", memory.get("_id"))
                    if memory_id:
                        # 直接尝试移动，move_memory_to_history内部会检查记忆是否存在
                        success = self.memory_module.move_memory_to_history(memory_id)
                        if success:
                            moved_count += 1
                            logger.debug(f"成功移动记忆 {memory_id} 到历史存档")
                        else:
                            logger.debug(f"记忆 {memory_id} 移动失败或已不存在")
                logger.info(f"成功移动 {moved_count}/{len(cluster['memories'])} 条记忆到历史存档")

                updated_beliefs.append(updated_belief)
                actions_taken.append(action_detail)
            
            return {
                "status": "success", 
                "user_id": user_id, 
                "processed_clusters": len(significant_clusters),
                "updated_beliefs": [b["node_id"] for b in updated_beliefs],
                "details": actions_taken
            }
            
        except Exception as e:
            logger.error(f"处理用户 '{user_id}' 的认知流程时发生错误: {e}", exc_info=True)
            return {"status": "error", "user_id": user_id, "error": str(e)}

    def _calculate_cluster_emotional_intensity(self, cluster: Dict[str, Any]) -> float:
        """
        计算记忆簇的平均情感强度

        Args:
            cluster: 记忆簇，包含memories列表

        Returns:
            float: 平均情感强度 (0-1)，0表示完全中性，1表示极度情绪化
        """
        memories = cluster.get("memories", [])
        if not memories:
            return 0.5  # 默认中性值

        total_intensity = 0.0
        valid_memories = 0

        for memory in memories:
            emotional_impact = memory.get("emotional_impact", {})
            if not emotional_impact:
                continue

            # 计算情感强度：所有情感维度偏离中性值(0.5)的程度
            intensity = 0.0
            emotion_count = 0

            for emotion_name, emotion_value in emotional_impact.items():
                if isinstance(emotion_value, (int, float)):
                    # 计算偏离中性值的程度
                    deviation = abs(emotion_value - 0.5)
                    intensity += deviation
                    emotion_count += 1

            if emotion_count > 0:
                # 平均偏离程度，然后乘以2映射到0-1范围
                memory_intensity = (intensity / emotion_count) * 2
                total_intensity += memory_intensity
                valid_memories += 1

        if valid_memories == 0:
            return 0.5  # 默认中性值

        average_intensity = total_intensity / valid_memories
        # 确保结果在0-1范围内
        return max(0.0, min(1.0, average_intensity))

    def find_significant_clusters(self, stm_queue: List[Dict[str, Any]], conflict_threshold: float = 0.05) -> List[Dict[str, Any]]:
        """
        对短期记忆队列进行聚类，找出显著的议题簇。
        使用k-means算法基于记忆的embedding向量进行聚类。
        只有冲突度达到阈值的簇才会被返回。
        
        Args:
            stm_queue: 短期记忆队列
            conflict_threshold: 冲突度阈值，只有超过此阈值的簇才会被返回
        
        Returns:
            显著议题簇列表
        """
        logger.info(f"正在对 {len(stm_queue)} 条短期记忆进行聚类分析...")
        
        # 提取记忆的embedding向量
        memory_embeddings = []
        valid_memories = []
        
        for memory in stm_queue:
            embedding = memory.get("embedding")
            if embedding and isinstance(embedding, list):
                memory_embeddings.append(embedding)
                valid_memories.append(memory)
        
        if not valid_memories:
            return []

        # 对于轮廓系数计算，需要至少3个样本才能进行有效聚类
        # 当样本数量为2时，直接将它们作为一个簇处理
        if len(valid_memories) < 2:
            logger.info(f"有效记忆数量 ({len(valid_memories)}) 过少，不进行聚类")
            return []
        elif len(valid_memories) == 2:
            logger.info("有效记忆数量为2，直接作为单个簇处理")
            # 直接将两个记忆作为一个簇
            cluster = {
                "cluster_id": f"cluster_{uuid.uuid4().hex[:8]}",
                "size": 2,
                "memories": valid_memories,
                "conflict_score": self.calculate_cluster_conflict(valid_memories),
                "centroid": np.mean(memory_embeddings, axis=0).tolist()
            }
            # 只有冲突度达到阈值的簇才会被返回
            if cluster["conflict_score"] >= conflict_threshold:
                logger.info("找到 1 个显著议题簇")
                return [cluster]
            else:
                logger.info(f"忽略冲突度为 {cluster['conflict_score']:.4f} 的簇 (低于阈值 {conflict_threshold})")
                logger.info("找到 0 个显著议题簇")
                return []

        # 对于3个或更多记忆，使用正常的聚类算法
        embeddings_array = np.array(memory_embeddings)

        # 确定最佳聚类数量 (2-10之间，或者记忆数量的一半，取较小值)
        # 对于轮廓系数，我们需要至少2个聚类，且样本数量必须大于聚类数量
        max_clusters = min(10, len(valid_memories) - 1)  # 确保样本数 > 聚类数
        if max_clusters < 2:
            max_clusters = 2

        best_k = 2  # 默认聚类数
        best_score = -1

        # 使用轮廓系数确定最佳聚类数量
        for k in range(2, max_clusters + 1):
            try:
                # 确保聚类数不超过样本数-1
                if k >= len(valid_memories):
                    continue

                kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(embeddings_array)

                # 如果有些聚类为空，跳过这个k值
                if len(np.unique(cluster_labels)) < k:
                    continue

                score = silhouette_score(embeddings_array, cluster_labels)

                if score > best_score:
                    best_score = score
                    best_k = k
            except Exception as e:
                logger.warning(f"计算k={k}的轮廓系数时出错: {e}")
                continue
        
        # 使用最佳聚类数量进行最终聚类
        kmeans = KMeans(n_clusters=best_k, random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(embeddings_array)
        
        # 构建聚类结果
        clusters = {}
        for i, label in enumerate(cluster_labels):
            if label not in clusters:
                clusters[label] = []
            clusters[label].append(valid_memories[i])
        
        # 转换为列表格式，并添加簇的元数据
        significant_clusters = []
        for label, memories in clusters.items():
            if len(memories) >= 2:  # 至少有一条记忆才形成簇
                # 计算簇的冲突度
                conflict_score = self.calculate_cluster_conflict(memories)
                
                # 只有冲突度达到阈值的簇才会被添加
                if conflict_score >= conflict_threshold:
                    cluster = {
                        "cluster_id": f"cluster_{uuid.uuid4().hex[:8]}",
                        "size": len(memories),
                        "memories": memories,
                        "conflict_score": conflict_score,
                        "centroid": kmeans.cluster_centers_[label].tolist()
                    }
                    significant_clusters.append(cluster)
                    logger.info(f"添加冲突度为 {conflict_score:.4f} 的簇")
                else:
                    logger.info(f"忽略冲突度为 {conflict_score:.4f} 的簇 (低于阈值 {conflict_threshold})")
        
        logger.info(f"找到 {len(significant_clusters)} 个显著议题簇")
        return significant_clusters

    def calculate_cluster_conflict(self, memories: List[Dict[str, Any]]) -> float:
        """
        计算一个记忆簇内部的冲突度。
        使用记忆簇内部情感向量之间的平均欧氏距离来衡量冲突度。
        
        Args:
            memories: 记忆列表
        
        Returns:
            冲突度分数 (0.0 - 1.0)
        """
        if len(memories) <= 1:
            return 0.0
        
        # 提取情感向量
        emotion_vectors = []
        for memory in memories:
            emotional_impact = memory.get("emotional_impact", {})
            if isinstance(emotional_impact, dict) and len(emotional_impact) > 0:
                # 过滤掉非数值型情感维度
                emotion_vector = {}
                for dimension, value in emotional_impact.items():
                    if isinstance(value, (int, float)):
                        emotion_vector[dimension] = value
                
                if emotion_vector:
                    emotion_vectors.append(emotion_vector)
        
        if len(emotion_vectors) <= 1:
            return 0.0
        
        # 获取所有情感维度
        all_dimensions = set()
        for vector in emotion_vectors:
            all_dimensions.update(vector.keys())
        
        # 将情感向量转换为完整的向量表示（缺失维度补0）
        complete_vectors = []
        for vector in emotion_vectors:
            complete_vector = [vector.get(dim, 0.0) for dim in all_dimensions]
            complete_vectors.append(complete_vector)
        
        # 计算所有向量对之间的欧氏距离
        total_distance = 0.0
        pair_count = 0
        
        for i in range(len(complete_vectors)):
            for j in range(i+1, len(complete_vectors)):
                # 计算两个向量之间的欧氏距离
                squared_diff_sum = sum((complete_vectors[i][k] - complete_vectors[j][k])**2 
                                      for k in range(len(complete_vectors[i])))
                distance = math.sqrt(squared_diff_sum)
                
                total_distance += distance
                pair_count += 1
        
        # 计算平均距离
        if pair_count > 0:
            avg_distance = total_distance / pair_count
            
            # 归一化到 [0, 1] 范围
            # 假设最大可能距离为sqrt(维度数*4)，即每个维度最大差值为2（-1到1）
            max_possible_distance = math.sqrt(len(all_dimensions) * 4) if all_dimensions else 1.0
            normalized_distance = min(avg_distance / max_possible_distance, 1.0) if max_possible_distance > 0 else 0.0
            
            return normalized_distance
        else:
            return 0.0

    def calculate_cosine_similarity(self, vec1: List[float], vec2: List[float]) -> float:
        """
        计算两个向量之间的余弦相似度。

        Args:
            vec1: 第一个向量
            vec2: 第二个向量

        Returns:
            余弦相似度分数 (0.0 - 1.0)，1.0表示完全相似
        """
        try:
            if not vec1 or not vec2 or len(vec1) != len(vec2):
                return 0.0

            # 转换为numpy数组
            v1 = np.array(vec1).reshape(1, -1)
            v2 = np.array(vec2).reshape(1, -1)

            # 计算余弦相似度
            similarity = cosine_similarity(v1, v2)[0][0]

            # 确保返回值在0-1范围内
            return max(0.0, min(1.0, similarity))
        except Exception as e:
            logger.warning(f"计算余弦相似度时出错: {e}")
            return 0.0

    def find_most_similar_memory_to_beliefs(self, user_id: str, memory_propositions: List[str], memory_embeddings: List[List[float]]) -> Tuple[str, int, Optional[Dict[str, Any]], float]:
        """
        从记忆命题中找到与用户现有信念最相似的记忆。
        同时返回最相似的信念对象，避免后续重复计算。

        Args:
            user_id: 用户ID
            memory_propositions: 记忆命题列表
            memory_embeddings: 对应的embedding向量列表

        Returns:
            (最相似的记忆命题, 记忆索引, 最相似的信念对象, 最大相似度分数)
        """
        if not memory_propositions or not memory_embeddings:
            return memory_propositions[0] if memory_propositions else "", 0, None, 0.0

        try:
            # 获取用户的所有信念
            query = {"term": {"owner_user_id": user_id}}
            response = ES_CLIENT.search(
                index=BELIEFS_INDEX,
                query=query,
                size=5000  # 调整为符合ES限制的大小
            )

            if response["hits"]["total"]["value"] == 0:
                # 如果用户没有任何信念，返回第一个记忆命题
                logger.info(f"用户 '{user_id}' 没有任何信念，返回第一个记忆命题")
                return memory_propositions[0], 0, None, 0.0

            # 提取所有信念的embedding向量和信念对象
            belief_embeddings = []
            belief_objects = []
            for hit in response["hits"]["hits"]:
                belief = hit["_source"]
                belief["node_id"] = hit["_id"]
                embedding = belief.get("embedding")
                if embedding and isinstance(embedding, list):
                    belief_embeddings.append(embedding)
                    belief_objects.append(belief)

            if not belief_embeddings:
                logger.info(f"用户 '{user_id}' 的信念没有有效的embedding向量")
                return memory_propositions[0], 0, None, 0.0

            # 计算每个记忆与所有信念的相似度，记录最佳匹配
            max_similarity = -1.0
            best_memory_index = 0
            best_belief_object = None

            for i, memory_embedding in enumerate(memory_embeddings):
                # 计算当前记忆与所有信念的相似度
                for j, belief_embedding in enumerate(belief_embeddings):
                    similarity = self.calculate_cosine_similarity(memory_embedding, belief_embedding)

                    # 更新全局最佳匹配
                    if similarity > max_similarity:
                        max_similarity = similarity
                        best_memory_index = i
                        best_belief_object = belief_objects[j]

            logger.info(f"找到最相似的记忆命题 (索引 {best_memory_index}), 最大相似度: {max_similarity:.4f}")
            if best_belief_object:
                logger.info(f"对应的最相似信念: '{best_belief_object['node_id']}'")

            return memory_propositions[best_memory_index], best_memory_index, best_belief_object, max_similarity

        except Exception as e:
            logger.error(f"查找最相似记忆时发生错误: {e}", exc_info=True)
            return memory_propositions[0], 0, None, 0.0

    def update_or_create_belief_from_cluster(self, user_id: str, cluster: Dict[str, Any]) -> Tuple[Dict[str, Any], float]:
        """
        根据记忆簇更新或创建信念。
        不再生成综合性命题，而是从簇中找到与用户现有信念最相似的记忆作为备选信念。

        Args:
            user_id: 用户ID
            cluster: 记忆簇

        Returns:
            更新后的信念对象和认知冲击量
        """
        logger.info(f"正在从记忆簇 '{cluster['cluster_id']}' 更新或创建信念...")

        # 1. 提取簇中的所有记忆命题和对应的embedding向量
        memory_propositions = []
        memory_embeddings = []

        for memory in cluster["memories"]:
            proposition = memory.get("core_proposition", "")
            embedding = memory.get("embedding")

            if proposition and embedding and isinstance(embedding, list):
                memory_propositions.append(proposition)
                memory_embeddings.append(embedding)

        if not memory_propositions:
            logger.error("记忆簇中没有有效的记忆命题或embedding向量，无法创建或更新信念。")
            return {}, 0.0

        # 2. 从记忆命题中找到与用户现有信念最相似的记忆作为备选信念
        # 同时获取最相似的信念对象，避免重复计算
        representative_proposition, selected_index, most_similar_belief, max_similarity = self.find_most_similar_memory_to_beliefs(
            user_id, memory_propositions, memory_embeddings
        )
        logger.info(f"选择的备选信念命题: '{representative_proposition}'")

        # 3. 判断是否存在足够相似的信念（使用已计算的相似度）
        embedding_threshold = 0.9  # embedding相似度阈值
        existing_belief = None

        if most_similar_belief and max_similarity >= embedding_threshold:
            # 如果embedding相似度足够高，直接使用已找到的信念
            existing_belief = most_similar_belief
            logger.info(f"通过embedding找到相似信念 '{existing_belief['node_id']}', 相似度: {max_similarity:.4f}")
        else:
            # 如果embedding相似度不够，回退到重排序模型
            logger.info(f"embedding相似度 ({max_similarity:.4f}) 低于阈值 ({embedding_threshold})，创建节点")
            

        # 4. 根据记忆簇计算证据权重，传入选择的命题
        if self.multithreading_config["enabled"]:
            logger.info("使用多线程模式聚合证据")
            s_w, c_w, avg_emotion = self.aggregate_evidence_from_cluster_multithreaded(cluster, representative_proposition)
        else:
            logger.info("使用单线程模式聚合证据")
            s_w, c_w, avg_emotion = self.aggregate_evidence_from_cluster(cluster, representative_proposition)
        
        # 5. 根据是否存在相似信念，选择更新或创建
        if existing_belief:
            # 确定新证据与现有信念的关系
            relation = self.ai_service.determine_detailed_belief_relationship(
                representative_proposition, 
                existing_belief["belief_summary"]
            )
            
            # 只有当关系类别为"支持"时才更新现有信念，否则创建新信念
            relation_category = relation.get("relation_category", "无关")
            if relation_category == "支持":
                logger.info("新证据与现有信念关系为'支持'，更新现有信念")
                updated_belief, deltaV = self.update_existing_belief(existing_belief, s_w, c_w, avg_emotion, relation, cluster["memories"])
                return updated_belief, deltaV
            else:
                logger.info(f"新证据与现有信念关系为'{relation_category}'，创建新信念而非更新")
                new_belief, deltaV = self.create_new_belief(user_id, representative_proposition, cluster["memories"], s_w, c_w, avg_emotion)
                return new_belief, deltaV
        else:
            # 创建新信念
            new_belief, deltaV = self.create_new_belief(user_id, representative_proposition, cluster["memories"], s_w, c_w, avg_emotion)
            return new_belief, deltaV

    def find_similar_belief(self, user_id: str, proposition: str, proposition_embedding: List[float] = None) -> Optional[Dict[str, Any]]:
        """
        在用户的信念网络中搜索与给定命题相似的信念。
        优先使用embedding向量相似度计算，如果失败则回退到重排序模型。

        Args:
            user_id: 用户ID
            proposition: 要搜索的命题
            proposition_embedding: 命题的embedding向量，如果提供则优先使用向量相似度

        Returns:
            相似的信念对象，如果不存在则返回None
        """
        logger.info(f"正在为用户 '{user_id}' 搜索与 '{proposition[:20]}...' 相似的信念...")

        try:
            # 第一步：获取用户的所有信念，不限制数量
            query = {
                "term": {"owner_user_id": user_id}
            }

            response = ES_CLIENT.search(
                index=BELIEFS_INDEX,
                query=query,
                size=5000  # 调整为符合ES限制的大小
            )

            if response["hits"]["total"]["value"] == 0:
                logger.info(f"用户 '{user_id}' 没有任何信念")
                return None

            # 第二步：准备信念数据
            belief_summaries = []
            belief_objects = []
            belief_embeddings = []

            for hit in response["hits"]["hits"]:
                belief = hit["_source"]
                belief["node_id"] = hit["_id"]
                belief_summary = belief.get("belief_summary", "")
                belief_embedding = belief.get("embedding")

                if belief_summary:
                    belief_summaries.append(belief_summary)
                    belief_objects.append(belief)
                    belief_embeddings.append(belief_embedding if belief_embedding else [])

            if not belief_summaries:
                logger.info(f"用户 '{user_id}' 的信念没有有效的摘要")
                return None

            # 第三步：选择相似度计算方法
            if proposition_embedding and all(emb for emb in belief_embeddings):
                # 使用embedding向量相似度计算
                logger.info("使用embedding向量相似度计算")
                max_similarity = -1.0
                best_belief_index = 0

                for i, belief_embedding in enumerate(belief_embeddings):
                    if belief_embedding:  # 确保embedding不为空
                        similarity = self.calculate_cosine_similarity(proposition_embedding, belief_embedding)
                        if similarity > max_similarity:
                            max_similarity = similarity
                            best_belief_index = i

                # 设置embedding相似度阈值
                embedding_threshold = 0.7  # embedding相似度阈值
                if max_similarity >= embedding_threshold:
                    most_similar_belief = belief_objects[best_belief_index]
                    logger.info(f"通过embedding找到相似信念 '{most_similar_belief['node_id']}', 相似度: {max_similarity:.4f}")
                    return most_similar_belief
                else:
                    logger.info(f"embedding最大相似度 ({max_similarity:.4f}) 低于阈值 ({embedding_threshold})")

            # 第四步：回退到重排序模型
            logger.info("回退到重排序模型进行相似度计算")
            reranked_results = self.ai_service.rerank_documents(proposition, belief_summaries)

            if not reranked_results:
                logger.info("重排序模型未返回结果")
                return None

            # 第五步：获取排名最高的信念
            top_result = reranked_results[0]
            top_score = top_result["score"]
            top_index = top_result["index"]

            # 重排序模型阈值
            rerank_threshold = 0.2
            if top_score >= rerank_threshold:
                most_similar_belief = belief_objects[top_index]
                logger.info(f"通过重排序找到相似信念 '{most_similar_belief['node_id']}', 相似度分数: {top_score:.4f}")
                return most_similar_belief
            else:
                logger.info(f"重排序最相似分数 ({top_score:.4f}) 低于阈值 ({rerank_threshold})")
                return None

        except Exception as e:
            logger.error(f"搜索相似信念时发生错误: {e}", exc_info=True)
            return None

    def aggregate_evidence_from_cluster(self, cluster: Dict[str, Any], reference_proposition: str = None) -> Tuple[float, float, Dict[str, float]]:
        """
        从记忆簇中聚合证据权重和情感。
        
        Args:
            cluster: 记忆簇
            reference_proposition: 参考核心命题，如果提供则使用此命题作为参考点
        
        Returns:
            支持证据权重, 反对证据权重, 平均情感向量
        """
        logger.info(f"正在聚合记忆簇 '{cluster['cluster_id']}' 的证据...")
        
        # 初始化支持和反对权重
        support_weight = 0.0
        contradict_weight = 0.0
        
        # 收集情感值和对应的可信度权重
        emotions_with_weights = []
        total_credibility = 0.0
        
        # 如果没有提供参考命题，则从记忆中提取
        if reference_proposition is None:
            # 提取簇中的记忆命题作为参考点
            memory_propositions = [memory.get("core_proposition", "") for memory in cluster["memories"]]
            memory_propositions = [p for p in memory_propositions if p]
            
            if not memory_propositions:
                logger.warning(f"记忆簇中没有有效的记忆命题")
                return 0.0, 0.0, {}
            
            # 使用第一个命题作为参考
            reference_proposition = memory_propositions[0]
        
        for memory in cluster["memories"]:
            # 从记忆中提取可信度
            source = memory.get("source", {})
            credibility = source.get("credibility_score", 0.5)
            total_credibility += credibility
            
            # 获取记忆的核心命题
            memory_proposition = memory.get("core_proposition", "")
            
            if memory_proposition and memory_proposition != reference_proposition:
                # 使用大模型判断命题间的关系
                relation = self.ai_service.determine_detailed_belief_relationship(memory_proposition, reference_proposition)
                relation_category = relation.get("relation_category", "支持")
                relation_weight = relation.get("weight", 0.5)
                relation_confidence = relation.get("confidence", 0.5)
                # Defensive coding: ensure values are numbers
                if not isinstance(relation_weight, (int, float)):
                    logger.warning(f"AI returned non-numeric weight: {relation_weight}. Defaulting to 0.5.")
                    relation_weight = 0.5
                if not isinstance(relation_confidence, (int, float)):
                    logger.warning(f"AI returned non-numeric confidence: {relation_confidence}. Defaulting to 0.5.")
                    relation_confidence = 0.5
                
                # 根据关系类型和可信度调整权重
                if relation_category == "支持":
                    # 支持证据
                    support_weight += credibility * relation_weight * relation_confidence
                elif relation_category == "反驳":
                    # 反对证据
                    contradict_weight += credibility * relation_weight * relation_confidence
                else:  # "无关"
                    # 无关的情况下，根据可信度轻微调整支持权重
                    support_weight += credibility * 0.1
            else:
                # 如果是参考命题本身或没有命题，视为支持
                support_weight += credibility
            
            # 收集情感数据和对应的可信度
            emotional_impact = memory.get("emotional_impact", {})
            # 确保emotional_impact是字典格式，并且包含有效的数值
            if isinstance(emotional_impact, dict) and emotional_impact:
                # 验证情感数据的有效性
                valid_emotion = {}
                for key, value in emotional_impact.items():
                    try:
                        # 确保值是数字类型
                        if isinstance(value, (int, float)):
                            valid_emotion[key] = float(value)
                        elif isinstance(value, str):
                            # 尝试将字符串转换为浮点数
                            valid_emotion[key] = float(value)
                        else:
                            logger.warning(f"情感维度 '{key}' 的值类型无效: {type(value)}, 使用默认值 0.5")
                            valid_emotion[key] = 0.5
                    except (ValueError, TypeError):
                        logger.warning(f"无法转换情感维度 '{key}' 的值 '{value}' 为数字，使用默认值 0.5")
                        valid_emotion[key] = 0.5

                if valid_emotion:
                    emotions_with_weights.append((valid_emotion, credibility))
            elif isinstance(emotional_impact, str):
                logger.warning(f"记忆 {memory.get('memory_id', 'unknown')} 的emotional_impact是字符串格式，跳过")
            elif emotional_impact:
                logger.warning(f"记忆 {memory.get('memory_id', 'unknown')} 的emotional_impact格式无效: {type(emotional_impact)}")
        
        # 计算加权平均情感
        weighted_emotion = {}
        if emotions_with_weights and total_credibility > 0:
            # 获取所有情感维度
            all_dimensions = set()
            for emotion, _ in emotions_with_weights:
                all_dimensions.update(emotion.keys())
            
            # 计算每个维度的加权平均值
            for dimension in all_dimensions:
                try:
                    weighted_sum = 0.0
                    for emotion, weight in emotions_with_weights:
                        if isinstance(emotion, dict):
                            emotion_value = emotion.get(dimension, 0.0)
                            # 确保emotion_value是数字类型
                            if isinstance(emotion_value, (int, float)):
                                weighted_sum += float(emotion_value) * float(weight)
                            elif isinstance(emotion_value, str):
                                try:
                                    weighted_sum += float(emotion_value) * float(weight)
                                except ValueError:
                                    logger.warning(f"无法转换情感值 '{emotion_value}' 为数字，跳过")
                            else:
                                logger.warning(f"情感维度 '{dimension}' 的值类型无效: {type(emotion_value)}")
                        else:
                            logger.warning(f"情感数据不是字典类型: {type(emotion)}")

                    weighted_emotion[dimension] = weighted_sum / total_credibility if total_credibility > 0 else 0.0
                except Exception as e:
                    logger.error(f"计算情感维度 '{dimension}' 时出错: {str(e)}")
                    weighted_emotion[dimension] = 0.0
        
        logger.info(f"聚合结果 - 支持权重: {support_weight:.4f}, 反对权重: {contradict_weight:.4f}")
        return support_weight, contradict_weight, weighted_emotion

    def _process_single_memory_relationship(self, memory_data: Dict[str, Any], reference_proposition: str) -> Dict[str, Any]:
        """
        处理单个记忆关系的工作函数，用于多线程执行

        Args:
            memory_data: 记忆数据
            reference_proposition: 参考命题

        Returns:
            包含关系信息和权重的字典
        """
        result = {
            "credibility": 0.0,
            "support_weight": 0.0,
            "contradict_weight": 0.0,
            "emotional_impact": {},
            "error": None
        }

        try:
            # 从记忆中提取可信度
            source = memory_data.get("source", {})
            credibility = source.get("credibility_score", 0.5)
            result["credibility"] = credibility

            # 获取记忆的核心命题
            memory_proposition = memory_data.get("core_proposition", "")

            if memory_proposition and memory_proposition != reference_proposition:
                # 使用大模型判断命题间的关系
                relation = self.ai_service.determine_detailed_belief_relationship(memory_proposition, reference_proposition)
                relation_category = relation.get("relation_category", "支持")
                relation_weight = relation.get("weight", 0.5)
                relation_confidence = relation.get("confidence", 0.5)

                # Defensive coding: ensure values are numbers
                if not isinstance(relation_weight, (int, float)):
                    logger.warning(f"AI returned non-numeric weight: {relation_weight}. Defaulting to 0.5.")
                    relation_weight = 0.5
                if not isinstance(relation_confidence, (int, float)):
                    logger.warning(f"AI returned non-numeric confidence: {relation_confidence}. Defaulting to 0.5.")
                    relation_confidence = 0.5

                # 根据关系类型和可信度调整权重
                if relation_category == "支持":
                    result["support_weight"] = credibility * relation_weight * relation_confidence
                elif relation_category == "反驳":
                    result["contradict_weight"] = credibility * relation_weight * relation_confidence
                else:  # "无关"
                    result["support_weight"] = credibility * 0.1
            else:
                # 如果是参考命题本身或没有命题，视为支持
                result["support_weight"] = credibility

            # 收集情感数据
            emotional_impact = memory_data.get("emotional_impact", {})
            if isinstance(emotional_impact, dict) and emotional_impact:
                # 验证情感数据的有效性
                valid_emotion = {}
                for key, value in emotional_impact.items():
                    try:
                        if isinstance(value, (int, float)):
                            valid_emotion[key] = float(value)
                        elif isinstance(value, str):
                            valid_emotion[key] = float(value)
                        else:
                            logger.warning(f"情感维度 '{key}' 的值类型无效: {type(value)}, 使用默认值 0.5")
                            valid_emotion[key] = 0.5
                    except (ValueError, TypeError):
                        logger.warning(f"无法转换情感维度 '{key}' 的值 '{value}' 为数字，使用默认值 0.5")
                        valid_emotion[key] = 0.5

                result["emotional_impact"] = valid_emotion

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"处理记忆关系时发生错误: {e}")

        return result

    def aggregate_evidence_from_cluster_multithreaded(self, cluster: Dict[str, Any], reference_proposition: str = None) -> Tuple[float, float, Dict[str, float]]:
        """
        从记忆簇中聚合证据权重和情感（多线程版本）。

        Args:
            cluster: 记忆簇
            reference_proposition: 参考核心命题，如果提供则使用此命题作为参考点

        Returns:
            支持证据权重, 反对证据权重, 平均情感向量
        """
        logger.info(f"正在聚合记忆簇 '{cluster['cluster_id']}' 的证据（多线程模式）...")

        # 如果没有提供参考命题，则从记忆中提取
        if reference_proposition is None:
            memory_propositions = [memory.get("core_proposition", "") for memory in cluster["memories"]]
            memory_propositions = [p for p in memory_propositions if p]

            if not memory_propositions:
                logger.warning("记忆簇中没有有效的记忆命题")
                return 0.0, 0.0, {}

            reference_proposition = memory_propositions[0]

        # 初始化累积变量
        total_support_weight = 0.0
        total_contradict_weight = 0.0
        emotions_with_weights = []
        total_credibility = 0.0

        # 使用多线程处理记忆关系判断
        max_workers = self.multithreading_config["max_workers"]
        timeout = self.multithreading_config["timeout"]

        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 创建部分函数，固定reference_proposition参数
            process_func = partial(self._process_single_memory_relationship, reference_proposition=reference_proposition)

            # 提交所有任务
            future_to_memory = {
                executor.submit(process_func, memory): memory
                for memory in cluster["memories"]
            }

            # 收集结果
            for future in concurrent.futures.as_completed(future_to_memory, timeout=timeout):
                memory = future_to_memory[future]
                try:
                    result = future.result()

                    if result["error"]:
                        logger.error(f"处理记忆时出错: {result['error']}")
                        continue

                    # 累积权重
                    total_support_weight += result["support_weight"]
                    total_contradict_weight += result["contradict_weight"]
                    total_credibility += result["credibility"]

                    # 收集情感数据
                    if result["emotional_impact"]:
                        emotions_with_weights.append((result["emotional_impact"], result["credibility"]))

                except concurrent.futures.TimeoutError:
                    logger.error(f"处理记忆超时")
                except Exception as e:
                    logger.error(f"处理记忆时发生异常: {e}")

        # 计算加权平均情感
        weighted_emotion = {}
        if emotions_with_weights and total_credibility > 0:
            # 获取所有情感维度
            all_dimensions = set()
            for emotion, _ in emotions_with_weights:
                all_dimensions.update(emotion.keys())

            # 计算每个维度的加权平均值
            for dimension in all_dimensions:
                try:
                    weighted_sum = 0.0
                    for emotion, weight in emotions_with_weights:
                        if isinstance(emotion, dict):
                            emotion_value = emotion.get(dimension, 0.0)
                            if isinstance(emotion_value, (int, float)):
                                weighted_sum += float(emotion_value) * float(weight)
                            elif isinstance(emotion_value, str):
                                try:
                                    weighted_sum += float(emotion_value) * float(weight)
                                except ValueError:
                                    logger.warning(f"无法转换情感值 '{emotion_value}' 为数字，跳过")
                            else:
                                logger.warning(f"情感维度 '{dimension}' 的值类型无效: {type(emotion_value)}")
                        else:
                            logger.warning(f"情感数据不是字典类型: {type(emotion)}")

                    weighted_emotion[dimension] = weighted_sum / total_credibility if total_credibility > 0 else 0.0
                except Exception as e:
                    logger.error(f"计算情感维度 '{dimension}' 时出错: {str(e)}")
                    weighted_emotion[dimension] = 0.0

        logger.info(f"聚合结果（多线程模式） - 支持权重: {total_support_weight:.4f}, 反对权重: {total_contradict_weight:.4f}")
        return total_support_weight, total_contradict_weight, weighted_emotion

    def update_existing_belief(self, belief: Dict[str, Any], s_w: float, c_w: float, avg_emotion: Dict[str, float], relation: Dict[str, Any], evidence_memories: List[Dict[str, Any]]) -> Tuple[Dict[str, Any], float]:
        """
        更新现有信念。
        
        Args:
            belief: 现有信念对象
            s_w: 支持证据权重
            c_w: 反对证据权重
            avg_emotion: 平均情感向量
            relation: 新证据与信念的关系
            evidence_memories: 新的证据记忆列表
        
        Returns:
            更新后的信念对象和真实度变化量(deltaV)
        """
        logger.info(f"正在更新信念 '{belief['node_id']}'...")
        
        # 提取当前值
        current_veracity = belief.get("veracity_score", 0.0)
        current_confidence = belief.get("confidence", 0.5)
        current_emotion = belief.get("emotional_disposition", {})
        
        # 根据关系类型更新真实度
        relation_category = relation.get("relation_category", "支持")
        relation_weight = relation.get("weight", 0.5)
        
        # 计算真实度的变化量
        delta_v_raw = s_w - c_w
        
        if relation_category == "支持":
            new_veracity = current_veracity + delta_v_raw * relation_weight*10
        elif relation_category == "反驳":
            new_veracity = current_veracity - delta_v_raw * relation_weight*10
        else:  # 无关
            new_veracity = current_veracity + delta_v_raw * 0.1  # 微小影响
        
        # 确保真实度在 [0, 1] 范围内
        new_veracity = max(0.0, min(1.0, new_veracity))
        
        # 更新置信度：使用更平滑的贝叶斯更新方法
        evidence_strength = s_w + c_w
        relation_confidence = relation.get("confidence", 0.5)

        # 计算证据的有效强度（考虑关系置信度）
        effective_strength = evidence_strength * relation_confidence

        # 使用贝叶斯更新的思想，避免置信度变化过于剧烈
        if relation_category == "支持":
            # 支持证据：增强置信度，但有衰减效应
            confidence_gain = (1 - current_confidence) * effective_strength * relation_weight
            new_confidence = min(current_confidence + confidence_gain,1)
        elif relation_category == "反驳":
            # 反驳证据：降低置信度，但不会降到极低
            confidence_loss = current_confidence * effective_strength * relation_weight 
            new_confidence = max(0,current_confidence - confidence_loss)
        else:  # 无关
            # 无关证据：微小影响
            weak_change = effective_strength * relation_weight * 0.02
            if current_confidence > 0.5:
                new_confidence = current_confidence - weak_change * 0.5  # 略微降低过高置信度
            else:
                new_confidence = current_confidence + weak_change * 0.5  # 略微提高过低置信度

        # 确保置信度在合理范围内，避免极值
        new_confidence = max(0.05, min(0.95, new_confidence))
        
        # 更新情感倾向
        # 获取智能体的情绪波动性
        try:
            agent_traits = self.agent_module.get_agent_traits(belief["owner_user_id"])
            emotional_volatility = agent_traits.get("emotional_volatility", 0.5) if agent_traits else 0.5
        except Exception as e:
            logger.warning(f"获取用户 '{belief['owner_user_id']}' 的情绪波动性时出错: {e}。使用默认值 0.5")
            emotional_volatility = 0.5
        
        new_emotion = {}
        for dimension in set(list(current_emotion.keys()) + list(avg_emotion.keys())):
            current_value = current_emotion.get(dimension, 0.0)
            new_value = avg_emotion.get(dimension, 0.0)
            
            # 加权平均
            weight_current = current_confidence
            weight_new = emotional_volatility
            total_weight = weight_current + weight_new
            
            if total_weight > 0:
                new_emotion[dimension] = (current_value * weight_current + new_value * weight_new) / total_weight
            else:
                new_emotion[dimension] = new_value
        
        # 更新证据记忆ID
        new_memory_ids = [memory.get("memory_id", memory.get("_id", "unknown")) for memory in evidence_memories]
        new_memory_ids = [mem_id for mem_id in new_memory_ids if mem_id != "unknown"]
        existing_memory_ids = belief.get("evidence_memory_ids", [])
        updated_memory_ids = list(set(existing_memory_ids + new_memory_ids))

        # 准备更新文档
        update_doc = {
            "veracity_score": new_veracity,
            "confidence": new_confidence,
            "emotional_disposition": new_emotion,
            "evidence_memory_ids": updated_memory_ids
        }
        
        # 执行更新
        ES_CLIENT.update(
            index=BELIEFS_INDEX,
            id=belief["node_id"],
            doc=update_doc,
            refresh=True
        )
        
        # 返回更新后的信念和真实度变化量
        updated_belief = belief.copy()
        updated_belief.update(update_doc)
        
        # 记录更新信息，包括关系类型和权重
        relation_type = relation.get("relation_type", "未知")
        logger.info(f"信念 '{belief['node_id']}' 已更新 (关系: {relation_category}/{relation_type}, 权重: {relation_weight:.2f}):")
        logger.info(f"  - 真实度: {new_veracity:.4f}")
        logger.info(f"  - 置信度: {new_confidence:.4f}")
        
        return updated_belief, new_veracity - current_veracity

    def create_new_belief(self, user_id: str, belief_summary: str, evidence_memories: List[Dict[str, Any]], s_w: float, c_w: float, emotion: Dict[str, float]) -> Tuple[Dict[str, Any], float]:
        """
        创建新信念。
        
        Args:
            user_id: 用户ID
            belief_summary: 信念摘要
            evidence_memories: 支持该信念的记忆列表
            s_w: 支持证据权重
            c_w: 反对证据权重
            emotion: 情感向量
        
        Returns:
            新创建的信念对象和认知冲击量(deltaV)
        """
        logger.info(f"正在为用户 '{user_id}' 创建新信念: '{belief_summary[:30]}...'")
        
        # 生成唯一信念ID
        node_id = f"belief_{user_id}_{uuid.uuid4().hex[:8]}"
        
        # 计算初始真实度
        initial_veracity = s_w - c_w
        initial_veracity = max(-1.0, min(1.0, initial_veracity))
        
        # 计算初始置信度 - 使用更平滑的算法
        evidence_strength = s_w + c_w
        if evidence_strength > 0:
            # 基础置信度：支持与反对的差异比例
            raw_confidence = abs(s_w - c_w) / evidence_strength

            # 引入平滑因子，避免极值
            # 1. 证据强度调节：证据越强，置信度上限越高，但仍有上限
            strength_factor = min(evidence_strength, 2.0) / 2.0  # 最大为1.0

            # 2. 不确定性保留：即使证据很强也保留一定不确定性
            uncertainty_reserve = 0.05  # 保留5%的不确定性
            max_confidence = 0.95 - uncertainty_reserve * (1 - strength_factor)

            # 3. 平滑映射：使用sigmoid函数进行平滑
            import math
            # 将raw_confidence映射到更平滑的曲线
            smoothed_confidence = 1 / (1 + math.exp(-6 * (raw_confidence - 0.5)))

            # 4. 最终置信度：结合强度因子和平滑度
            initial_confidence = min(smoothed_confidence * strength_factor, max_confidence)

            # 确保最小置信度
            initial_confidence = max(initial_confidence, 0.1)
        else:
            initial_confidence = 0.5
        
        # 调整情感强度
        try:
            agent_traits = self.agent_module.get_agent_traits(user_id)
            emotional_volatility = agent_traits.get("emotional_volatility", 0.5) if agent_traits else 0.5
        except Exception as e:
            logger.warning(f"获取用户 '{user_id}' 的情绪波动性时出错: {e}。使用默认值 0.5")
            emotional_volatility = 0.5
        
        adjusted_emotion = {}
        for dimension, value in emotion.items():
            adjusted_emotion[dimension] = value * emotional_volatility
        
        # 提取记忆ID
        evidence_memory_ids = [memory.get("memory_id", memory.get("_id", "unknown")) for memory in evidence_memories]
        evidence_memory_ids = [mem_id for mem_id in evidence_memory_ids if mem_id != "unknown"]
        
        # 使用AI服务生成信念的embedding向量
        belief_embedding = self.ai_service.get_embedding(belief_summary)
        
        # 创建信念文档
        belief_doc = {
            "owner_user_id": user_id,
            "belief_summary": belief_summary,
            "embedding": belief_embedding,
            "veracity_score": initial_veracity,
            "confidence": initial_confidence,
            "emotional_disposition": adjusted_emotion,
            "evidence_memory_ids": evidence_memory_ids,
            "relation_edges": []  # 新信念暂时没有关联边
        }
        
        # 保存到Elasticsearch，并等待其可被搜索，以避免竞态条件
        ES_CLIENT.index(index=BELIEFS_INDEX, id=node_id, document=belief_doc, refresh=True)

        # 根据配置选择建立关系的方式
        if self.multithreading_config["enabled"]:
            logger.info("使用多线程模式建立信念关系")
            self.establish_belief_relationships_multithreaded(user_id, node_id, belief_summary)
        else:
            logger.info("使用单线程模式建立信念关系")
            self.establish_belief_relationships(user_id, node_id, belief_summary)
        
        # 返回创建的信念和认知冲击量
        belief_doc["node_id"] = node_id
        
        logger.info(f"成功创建信念 '{node_id}':")
        logger.info(f"  - 真实度: {initial_veracity:.4f}")
        logger.info(f"  - 置信度: {initial_confidence:.4f}")
        
        return belief_doc, initial_veracity

    def _process_single_belief_relationship(self, belief_summary: str, existing_belief_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理单个信念关系的工作函数，用于多线程执行

        Args:
            belief_summary: 新信念的摘要
            existing_belief_data: 现有信念的数据，包含id和summary

        Returns:
            包含关系信息的字典
        """
        existing_belief_id = existing_belief_data["id"]
        existing_belief_summary = existing_belief_data["summary"]

        result = {
            "existing_belief_id": existing_belief_id,
            "relation_a_to_b": None,
            "relation_b_to_a": None,
            "error": None
        }

        try:
            # 使用大模型判断新信念对现有信念的关系 (A->B)
            try:
                relation_a_to_b = self.ai_service.determine_detailed_belief_relationship(belief_summary, existing_belief_summary)

                # 确保返回的是字典格式
                if isinstance(relation_a_to_b, str):
                    logger.warning(f"AI服务返回字符串而非字典格式: {relation_a_to_b}")
                    relation_a_to_b = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}
                elif not isinstance(relation_a_to_b, dict):
                    logger.warning(f"AI服务返回格式无效: {type(relation_a_to_b)}")
                    relation_a_to_b = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}

                result["relation_a_to_b"] = relation_a_to_b
            except Exception as e:
                logger.error(f"判断关系 A->B 时出错: {str(e)}")
                result["relation_a_to_b"] = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}

            # 使用大模型判断现有信念对新信念的关系 (B->A)
            try:
                relation_b_to_a = self.ai_service.determine_detailed_belief_relationship(existing_belief_summary, belief_summary)

                # 确保返回的是字典格式
                if isinstance(relation_b_to_a, str):
                    logger.warning(f"AI服务返回字符串而非字典格式: {relation_b_to_a}")
                    relation_b_to_a = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}
                elif not isinstance(relation_b_to_a, dict):
                    logger.warning(f"AI服务返回格式无效: {type(relation_b_to_a)}")
                    relation_b_to_a = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}

                result["relation_b_to_a"] = relation_b_to_a
            except Exception as e:
                logger.error(f"判断关系 B->A 时出错: {str(e)}")
                result["relation_b_to_a"] = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}

        except Exception as e:
            result["error"] = str(e)
            logger.error(f"处理信念关系时发生错误: {e}")

        return result

    def establish_belief_relationships_multithreaded(self, user_id: str, new_belief_id: str, belief_summary: str) -> None:
        """
        为新创建的信念建立与其他信念的关系（多线程版本）。
        遍历所有现有信念，使用大模型判断节点之间的双向关系。

        Args:
            user_id: 用户ID
            new_belief_id: 新信念的ID
            belief_summary: 新信念的摘要
        """
        logger.info(f"正在为信念 '{new_belief_id}' 建立关系网络（多线程模式）...")

        # 获取用户的所有信念（排除新创建的信念）
        try:
            query = {
                "bool": {
                    "must": [
                        {"term": {"owner_user_id": user_id}},
                        {"bool": {"must_not": {"term": {"_id": new_belief_id}}}}
                    ]
                }
            }

            response = ES_CLIENT.search(
                index=BELIEFS_INDEX,
                query=query,
                size=10000,  # 获取所有信念
                _source=["belief_summary"]
            )

            if response["hits"]["total"]["value"] == 0:
                logger.info(f"用户 '{user_id}' 没有其他信念，无需建立关系")
                return

            # 准备现有信念数据
            existing_beliefs_data = []
            for hit in response["hits"]["hits"]:
                existing_beliefs_data.append({
                    "id": hit["_id"],
                    "summary": hit["_source"]["belief_summary"]
                })

            logger.info(f"找到 {len(existing_beliefs_data)} 个现有信念，开始多线程处理...")

            # 使用多线程处理关系判断
            relation_edges = []
            max_workers = self.multithreading_config["max_workers"]
            timeout = self.multithreading_config["timeout"]

            with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
                # 创建部分函数，固定belief_summary参数
                process_func = partial(self._process_single_belief_relationship, belief_summary)

                # 提交所有任务
                future_to_belief = {
                    executor.submit(process_func, belief_data): belief_data
                    for belief_data in existing_beliefs_data
                }

                # 收集结果
                for future in concurrent.futures.as_completed(future_to_belief, timeout=timeout):
                    belief_data = future_to_belief[future]
                    try:
                        result = future.result()

                        if result["error"]:
                            logger.error(f"处理信念 {belief_data['id']} 时出错: {result['error']}")
                            continue

                        # 处理A->B关系
                        relation_a_to_b = result["relation_a_to_b"]
                        if relation_a_to_b and relation_a_to_b.get("relation_category") != "无关":
                            edge_out = {
                                "target_node_id": belief_data["id"],
                                "relation_category": relation_a_to_b.get("relation_category", "无关"),
                                "relation_type": relation_a_to_b.get("relation_type"),
                                "weight": float(relation_a_to_b.get("weight", 0.0)),
                                "confidence": float(relation_a_to_b.get("confidence", 0.0))
                            }
                            relation_edges.append(edge_out)
                            logger.info(f"建立关系 '{new_belief_id}' -{edge_out['relation_category']}/{edge_out['relation_type']}-> '{belief_data['id']}' (权重: {edge_out['weight']:.2f}, 置信度: {edge_out['confidence']:.2f})")

                        # 处理B->A关系
                        relation_b_to_a = result["relation_b_to_a"]
                        if relation_b_to_a and relation_b_to_a.get("relation_category") != "无关":
                            edge_in = {
                                "target_node_id": new_belief_id,
                                "relation_category": relation_b_to_a.get("relation_category", "无关"),
                                "relation_type": relation_b_to_a.get("relation_type"),
                                "weight": float(relation_b_to_a.get("weight", 0.0)),
                                "confidence": float(relation_b_to_a.get("confidence", 0.0))
                            }

                            # 线程安全地更新现有信念的关系边
                            with self._thread_lock:
                                existing_belief_res = ES_CLIENT.get(index=BELIEFS_INDEX, id=belief_data["id"])
                                existing_edges = existing_belief_res["_source"].get("relation_edges", [])
                                existing_edges.append(edge_in)

                                ES_CLIENT.update(
                                    index=BELIEFS_INDEX,
                                    id=belief_data["id"],
                                    doc={"relation_edges": existing_edges},
                                    refresh=True
                                )

                            logger.info(f"建立关系 '{belief_data['id']}' -{edge_in['relation_category']}/{edge_in['relation_type']}-> '{new_belief_id}' (权重: {edge_in['weight']:.2f}, 置信度: {edge_in['confidence']:.2f})")

                    except concurrent.futures.TimeoutError:
                        logger.error(f"处理信念 {belief_data['id']} 超时")
                    except Exception as e:
                        logger.error(f"处理信念 {belief_data['id']} 时发生异常: {e}")

            # 更新新信念的关系边
            if relation_edges:
                ES_CLIENT.update(
                    index=BELIEFS_INDEX,
                    id=new_belief_id,
                    doc={"relation_edges": relation_edges},
                    refresh=True
                )

                logger.info(f"为信念 '{new_belief_id}' 建立了 {len(relation_edges)} 个关系（多线程模式）")
            else:
                logger.info(f"信念 '{new_belief_id}' 没有与其他信念建立关系（多线程模式）")

        except Exception as e:
            logger.error(f"建立信念关系时发生错误（多线程模式）: {e}", exc_info=True)

    def establish_belief_relationships(self, user_id: str, new_belief_id: str, belief_summary: str) -> None:
        """
        为新创建的信念建立与其他信念的关系。
        遍历所有现有信念，使用大模型判断节点之间的双向关系。
        
        Args:
            user_id: 用户ID
            new_belief_id: 新信念的ID
            belief_summary: 新信念的摘要
        """
        logger.info(f"正在为信念 '{new_belief_id}' 建立关系网络...")
        
        # 获取用户的所有信念（排除新创建的信念）
        try:
            query = {
                "bool": {
                    "must": [
                        {"term": {"owner_user_id": user_id}},
                        {"bool": {"must_not": {"term": {"_id": new_belief_id}}}}
                    ]
                }
            }
            
            response = ES_CLIENT.search(
                index=BELIEFS_INDEX,
                query=query,
                size=10000,  # 获取所有信念
                _source=["belief_summary"]
            )
            
            if response["hits"]["total"]["value"] == 0:
                logger.info(f"用户 '{user_id}' 没有其他信念，无需建立关系")
                return
            
            # 遍历所有现有信念，判断关系
            relation_edges = []
            for hit in response["hits"]["hits"]:
                existing_belief_id = hit["_id"]
                existing_belief_summary = hit["_source"]["belief_summary"]
                
                # 使用大模型判断新信念对现有信念的关系 (A->B)
                try:
                    relation_a_to_b = self.ai_service.determine_detailed_belief_relationship(belief_summary, existing_belief_summary)

                    # 确保返回的是字典格式
                    if isinstance(relation_a_to_b, str):
                        logger.warning(f"AI服务返回字符串而非字典格式: {relation_a_to_b}")
                        relation_a_to_b = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}
                    elif not isinstance(relation_a_to_b, dict):
                        logger.warning(f"AI服务返回格式无效: {type(relation_a_to_b)}")
                        relation_a_to_b = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}

                    relation_category_a_to_b = relation_a_to_b.get("relation_category", "无关")
                    relation_type_a_to_b = relation_a_to_b.get("relation_type")
                    weight_a_to_b = float(relation_a_to_b.get("weight", 0.0))
                    confidence_a_to_b = float(relation_a_to_b.get("confidence", 0.0))
                except Exception as e:
                    logger.error(f"判断关系 A->B 时出错: {str(e)}")
                    relation_category_a_to_b = "无关"
                    relation_type_a_to_b = None
                    weight_a_to_b = 0.0
                    confidence_a_to_b = 0.0

                # 使用大模型判断现有信念对新信念的关系 (B->A)
                try:
                    relation_b_to_a = self.ai_service.determine_detailed_belief_relationship(existing_belief_summary, belief_summary)

                    # 确保返回的是字典格式
                    if isinstance(relation_b_to_a, str):
                        logger.warning(f"AI服务返回字符串而非字典格式: {relation_b_to_a}")
                        relation_b_to_a = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}
                    elif not isinstance(relation_b_to_a, dict):
                        logger.warning(f"AI服务返回格式无效: {type(relation_b_to_a)}")
                        relation_b_to_a = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}
                except Exception as e:
                    logger.error(f"判断关系 B->A 时出错: {str(e)}")
                    relation_b_to_a = {"relation_category": "无关", "relation_type": None, "weight": 0.0, "confidence": 0.0}
                
                relation_category_b_to_a = relation_b_to_a.get("relation_category", "无关")
                relation_type_b_to_a = relation_b_to_a.get("relation_type")
                try:
                    weight_b_to_a = float(relation_b_to_a.get("weight", 0.0))
                    confidence_b_to_a = float(relation_b_to_a.get("confidence", 0.0))
                except (ValueError, TypeError):
                    logger.warning(f"无法转换权重或置信度为数字，使用默认值")
                    weight_b_to_a = 0.0
                    confidence_b_to_a = 0.0
                
                # 只有当关系不是"无关"且权重大于阈值时才建立边
                if relation_category_a_to_b != "无关" :
                    # 为新信念添加出边 (A->B)
                    edge_out = {
                        "target_node_id": existing_belief_id,
                        "relation_category": relation_category_a_to_b,
                        "relation_type": relation_type_a_to_b,
                        "weight": weight_a_to_b,
                        "confidence": confidence_a_to_b
                    }
                    relation_edges.append(edge_out)
                    logger.info(f"建立关系 '{new_belief_id}' -{relation_category_a_to_b}/{relation_type_a_to_b}-> '{existing_belief_id}' (权重: {weight_a_to_b:.2f}, 置信度: {confidence_a_to_b:.2f})")
                
                # 只有当关系不是"无关"且权重大于阈值时才建立边
                if relation_category_b_to_a != "无关" :
                    # 为现有信念添加出边 (B->A)
                    edge_in = {
                        "target_node_id": new_belief_id,
                        "relation_category": relation_category_b_to_a,
                        "relation_type": relation_type_b_to_a,
                        "weight": weight_b_to_a,
                        "confidence": confidence_b_to_a
                    }
                    
                    # 更新现有信念的关系边
                    existing_belief_res = ES_CLIENT.get(index=BELIEFS_INDEX, id=existing_belief_id)
                    existing_edges = existing_belief_res["_source"].get("relation_edges", [])
                    existing_edges.append(edge_in)
                    
                    ES_CLIENT.update(
                        index=BELIEFS_INDEX,
                        id=existing_belief_id,
                        doc={"relation_edges": existing_edges},
                        refresh=True
                    )
                    
                    logger.info(f"建立关系 '{existing_belief_id}' -{relation_category_b_to_a}/{relation_type_b_to_a}-> '{new_belief_id}' (权重: {weight_b_to_a:.2f}, 置信度: {confidence_b_to_a:.2f})")
            
            # 更新新信念的关系边
            if relation_edges:
                ES_CLIENT.update(
                    index=BELIEFS_INDEX,
                    id=new_belief_id,
                    doc={"relation_edges": relation_edges},
                    refresh=True
                )
                
                logger.info(f"为信念 '{new_belief_id}' 建立了 {len(relation_edges)} 个关系")
            else:
                logger.info(f"信念 '{new_belief_id}' 没有与其他信念建立关系")
                
        except Exception as e:
            logger.error(f"建立信念关系时发生错误: {e}", exc_info=True)

    def trigger_verification_action(self, user_id: str, belief_to_verify: Dict[str, Any]) -> Dict[str, Any]:
        """
        触发求证机制，生成一个新的搜索行为。

        Args:
            user_id: 用户ID
            belief_to_verify: 需要求证的信念

        Returns:
            生成的搜索行为
        """
        logger.info(f"正在为用户 '{user_id}' 触发对信念 '{belief_to_verify['node_id']}' 的求证机制...")

        # 直接从search.json中获取内容，不调用大模型生成搜索查询
        try:
            # 读取JSON文件
            topic_paths = Config.get_topic_data_paths()
            search_json_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), topic_paths['search_file'])
            with open(search_json_path, 'r', encoding='utf-8') as file:
                data = json.load(file)

            # 随机选择2个不同的索引
            total_entries = len(data)
            selected_indices = random.sample(range(total_entries), 2)

            # 获取选中条目的标题和内容
            selected_items = [data[i] for i in selected_indices]
            selected_statements = [item["content"] for item in selected_items]

            # 使用第一个选中条目的标题作为搜索查询
            search_query = selected_items[0]["title"]

            logger.info(f"从search.json中选择的搜索查询: {search_query}")
            logger.info("搜索结果:")
            for i, statement in enumerate(selected_statements, 1):
                logger.info(f"{i}. {statement}")

            # 使用搜索结果作为内容
            content = f"Search results for: {search_query}\n" + "\n".join(selected_statements)
        except Exception as e:
            logger.warning(f"从search.json读取内容时发生错误: {e}")
            # 备选方案：提供默认内容
            search_query = "法律相关信息查询"
            content = f"Searching for: {search_query}\n默认搜索结果1\n默认搜索结果2"

        # 构造搜索行为
        search_action = {
            "user_id": user_id,
            "type": "READ_POST",
            "content": content,
            "source": {
                "credibility_score": 0.7,  # 搜索结果通常有一定可信度
                "search_query": search_query,
                "verification_for": belief_to_verify["node_id"]
            },
            "timestamp": datetime.datetime.now(datetime.timezone.utc)
        }

        logger.info(f"生成了求证搜索行为: '{search_query}'")

        # 将求证行为加入队列，避免递归调用
        try:
            if self._engine_instance is not None:
                logger.info("将求证搜索行为加入处理队列...")
                self._engine_instance.add_verification_action_to_queue(search_action)
                logger.info("求证搜索行为已加入队列，将在当前认知处理完成后执行")
            else:
                logger.warning("引擎实例未设置，求证搜索行为未加入队列，仅返回行为对象")
        except Exception as e:
            logger.error(f"将求证搜索行为加入队列时发生错误: {e}")

        return search_action

    def propagate_belief_changes(self, updated_belief: Dict[str, Any], deltaV: float) -> List[Dict[str, Any]]:
        """
        将信念变化传播到相关联的其他信念。
        
        Args:
            updated_belief: 更新后的信念
            deltaV: 认知冲击量
        
        Returns:
            受影响的信念列表
        """
        logger.info(f"正在传播信念 '{updated_belief['node_id']}' 的变化，认知冲击为 {deltaV}...")
        
        affected_beliefs = []
        
        # 获取关联边
        relation_edges = updated_belief.get("relation_edges", [])
        if not relation_edges:
            logger.info(f"信念 '{updated_belief['node_id']}' 没有关联边，无需传播")
            return affected_beliefs
        
        # 遍历所有关联边
        for edge in relation_edges:
            target_node_id = edge.get("target_node_id")
            relation_category = edge.get("relation_category")
            relation_type = edge.get("relation_type")
            edge_weight = edge.get("weight", 0.5)
            
            if not target_node_id or not relation_category:
                continue
            
            try:
                # 获取目标信念
                target_belief_response = ES_CLIENT.get(index=BELIEFS_INDEX, id=target_node_id)
                target_belief = target_belief_response["_source"]
                target_belief["node_id"] = target_node_id
                
                # 根据关系类别和类型计算传播影响
                propagation_factor = 0.0
                
                if relation_category == "支持":
                    # 根据具体关系类型调整传播因子
                    if relation_type in ["imply", "is_prerequisite_for"]:
                        propagation_factor = 0.9  # 逻辑蕴含关系，强正向传播
                    elif relation_type in ["causes", "is_reason_for"]:
                        propagation_factor = 0.7  # 因果关系，较强正向传播
                    elif relation_type == "is_evidence_for":
                        propagation_factor = 0.5  # 证据关系，中等正向传播
                    elif relation_type == "is_example_of":
                        propagation_factor = 0.3  # 例证关系，弱正向传播
                    else:  # correlates_with 或其他
                        propagation_factor = 0.2  # 相关关系，很弱正向传播
                
                elif relation_category == "反驳":
                    # 根据具体关系类型调整传播因子
                    if relation_type in ["excludes", "is_incompatible_with"]:
                        propagation_factor = -0.9  # 逻辑互斥关系，强负向传播
                    elif relation_type == "is_counter_evidence_to":
                        propagation_factor = -0.7  # 反证关系，较强负向传播
                    elif relation_type in ["diminishes", "weakens"]:
                        propagation_factor = -0.5  # 削弱关系，中等负向传播
                    else:  # is_exception_to 或其他
                        propagation_factor = -0.3  # 例外关系，弱负向传播
                
                # 计算传播的认知冲击，使用边的权重
                propagated_deltaV = deltaV * propagation_factor * edge_weight
                
                if abs(propagated_deltaV) > 0.05:
                    # 获取当前信念值
                    current_veracity = target_belief.get("veracity_score", 0.0)
                    current_confidence = target_belief.get("confidence", 0.5)
                    
                    # 考虑边的置信度来调整传播影响
                    edge_confidence = edge.get("confidence", 0.5)
                    weighted_deltaV = propagated_deltaV * edge_confidence
                    
                    # 根据目标信念的当前置信度调整影响程度
                    # 高置信度的信念更难被改变
                    resistance_factor = current_confidence * 0.8
                    adjusted_deltaV = weighted_deltaV * (1 - resistance_factor)
                    
                    # 计算新的真实度
                    new_veracity = current_veracity + adjusted_deltaV
                    # 确保真实度在 [0.0, 1.0] 范围内
                    new_veracity = max(0.0, min(1.0, new_veracity))
                    
                    # 置信度变化：使用更平滑的传播机制
                    # 将veracity_score从[0,1]转换为[-1,1]来判断方向一致性
                    # 1. 如果新信息与当前信念一致（同方向），适度增加置信度
                    # 2. 如果新信息与当前信念冲突（反方向），适度降低置信度
                    current_veracity_centered = current_veracity - 0.5  # 转换为[-0.5, 0.5]
                    if (current_veracity_centered * adjusted_deltaV) >= 0:
                        # 信息一致，增强置信度但有上限
                        confidence_gain = (1 - current_confidence) * 0.08 * abs(adjusted_deltaV) * edge_confidence
                        confidence_change = confidence_gain
                    else:
                        # 信息冲突，降低置信度但不会过低
                        confidence_loss = current_confidence * 0.12 * abs(adjusted_deltaV) * edge_confidence
                        confidence_change = -confidence_loss

                    new_confidence = current_confidence + confidence_change
                    # 确保置信度在合理范围内，避免极值
                    new_confidence = max(0.05, min(0.95, new_confidence))
                    
                    # 更新目标信念
                    update_doc = {
                        "veracity_score": new_veracity,
                        "confidence": new_confidence
                    }
                    
                    ES_CLIENT.update(
                        index=BELIEFS_INDEX,
                        id=target_node_id,
                        doc=update_doc,
                        refresh=True
                    )
                    
                    # 更新返回对象
                    target_belief.update(update_doc)
                    affected_beliefs.append(target_belief)
                    
                    logger.info(f"信念 '{target_node_id}' 受到影响 (关系: {relation_category}/{relation_type}, 权重: {edge_weight:.2f}):")
                    logger.info(f"  - 真实度: {new_veracity:.4f}")
                    logger.info(f"  - 置信度: {new_confidence:.4f}")
            
            except Exception as e:
                logger.error(f"传播到信念 '{target_node_id}' 时发生错误: {e}", exc_info=True)
        
        return affected_beliefs

    def get_agent_beliefs(self, user_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """
        获取用户的所有信念。
        
        Args:
            user_id: 用户ID
            limit: 返回数量限制
        
        Returns:
            信念列表
        """
        try:
            query = {
                "term": {"owner_user_id": user_id}
            }
            
            response = ES_CLIENT.search(
                index=BELIEFS_INDEX,
                query=query,
                size=limit
            )
            
            beliefs = []
            for hit in response['hits']['hits']:
                belief = hit['_source']
                belief['node_id'] = hit['_id']
                beliefs.append(belief)
            
            return beliefs
            
        except Exception as e:
            logger.error(f"获取用户 '{user_id}' 的信念时发生错误: {e}", exc_info=True)
            return []

    def get_belief_by_id(self, belief_id: str) -> Optional[Dict[str, Any]]:
        """
        根据ID获取信念详情。
        
        Args:
            belief_id: 信念ID
        
        Returns:
            信念对象，如果不存在则返回None
        """
        try:
            response = ES_CLIENT.get(index=BELIEFS_INDEX, id=belief_id)
            belief = response['_source']
            belief['node_id'] = belief_id
            return belief
        except Exception as e:
            logger.error(f"获取信念 '{belief_id}' 时发生错误: {e}", exc_info=True)
            return None 