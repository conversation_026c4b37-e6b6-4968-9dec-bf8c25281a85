# 主题配置化系统使用指南

## 概述

本系统已经将原本硬编码的"婚姻和继承"主题提取到配置文件中，现在可以通过配置文件或环境变量轻松切换不同的讨论主题。

## 配置项说明

### 主要配置项

- `TOPIC_NAME`: 当前讨论主题的名称（默认: "婚姻和继承"）
- `TOPIC_DATA_DIR`: 主题数据文件夹名称（默认: "婚姻和继承"）

### 配置方式

#### 方式1: 环境变量（推荐）

```bash
export TOPIC_NAME="环境保护"
export TOPIC_DATA_DIR="environmental_protection"
```

#### 方式2: 修改配置文件

编辑 `config/config.py` 文件：

```python
# 主题配置
TOPIC_NAME = os.getenv("TOPIC_NAME", "环境保护")  # 修改默认值
TOPIC_DATA_DIR = os.getenv("TOPIC_DATA_DIR", "environmental_protection")  # 修改默认值
```

## 数据文件结构

每个主题需要在 `lib/` 目录下创建对应的数据文件夹，包含以下文件：

```
lib/
├── 婚姻和继承/           # 默认主题
│   ├── belief.json      # 信念数据
│   ├── post.json        # 帖子数据
│   └── search.json      # 搜索结果数据
└── your_new_topic/      # 新主题
    ├── belief.json
    ├── post.json
    └── search.json
```

## 使用示例

### 1. 查看当前配置

```python
from config.config import Config

# 获取主题配置
topic_config = Config.get_topic_config()
print(f"当前主题: {topic_config['topic_name']}")

# 获取数据文件路径
topic_paths = Config.get_topic_data_paths()
print(f"信念文件: {topic_paths['belief_file']}")
```

### 2. 切换主题

```bash
# 设置环境变量
export TOPIC_NAME="气候变化"
export TOPIC_DATA_DIR="climate_change"

# 运行程序
python your_script.py
```

### 3. 创建新主题

1. 创建数据文件夹：
   ```bash
   mkdir -p lib/climate_change
   ```

2. 准备数据文件：
   ```bash
   # 复制现有文件作为模板
   cp lib/婚姻和继承/belief.json lib/climate_change/
   cp lib/婚姻和继承/post.json lib/climate_change/
   cp lib/婚姻和继承/search.json lib/climate_change/
   ```

3. 编辑数据文件，替换为新主题的内容

4. 设置配置：
   ```bash
   export TOPIC_NAME="气候变化"
   export TOPIC_DATA_DIR="climate_change"
   ```

## 影响的模块

以下模块已经配置化，会自动使用新的主题配置：

- `src/modules/ai_service.py` - AI服务中的主题名称
- `src/modules/belief_module.py` - 信念模块的数据文件路径
- `src/modules/user_management.py` - 用户管理的数据文件路径
- `scripts/initialize_db.py` - 数据库初始化脚本
- `experiment/structure/community_based_initialize_db.py` - 社区结构初始化脚本

## 测试

运行测试脚本验证配置是否正确：

```bash
# 测试基本配置
python test_config.py

# 测试AI服务配置
python test_ai_service_topic.py

# 查看演示
python demo_topic_config.py
```

## 注意事项

1. **数据文件格式**: 新主题的数据文件必须保持与原始文件相同的JSON结构
2. **文件编码**: 所有JSON文件必须使用UTF-8编码
3. **重启应用**: 修改配置后需要重启应用程序才能生效
4. **路径检查**: 确保新主题的数据文件夹和文件都存在，否则程序可能报错

## 故障排除

### 常见问题

1. **ImportError**: 如果遇到导入错误，检查Python路径设置
2. **FileNotFoundError**: 确保数据文件存在且路径正确
3. **配置不生效**: 检查环境变量是否正确设置，或重启应用程序

### 调试方法

```python
# 检查当前配置
from config.config import Config
print(f"主题: {Config.TOPIC_NAME}")
print(f"数据目录: {Config.TOPIC_DATA_DIR}")

# 检查文件路径
import os
topic_paths = Config.get_topic_data_paths()
for file_type, path in topic_paths.items():
    full_path = os.path.join(os.getcwd(), path)
    print(f"{file_type}: {path} - {'存在' if os.path.exists(full_path) else '不存在'}")
```

## 总结

通过这次配置化改造，系统现在具备了以下优势：

1. **灵活性**: 可以轻松切换不同的讨论主题
2. **可维护性**: 不需要修改代码就能更换主题
3. **可扩展性**: 添加新主题只需要准备数据文件和设置配置
4. **环境友好**: 支持通过环境变量进行配置，适合不同部署环境
